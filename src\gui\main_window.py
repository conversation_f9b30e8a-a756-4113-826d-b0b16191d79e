"""
主窗口 - 匿名加密通讯系统GUI
提供用户友好的界面进行安全通信
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import asyncio
import threading
import json
import os
from typing import Dict, Optional, Callable
from datetime import datetime
import base64

from ..network import AnonymousClient, DEFAULT_NETWORK_CONFIG
from ..crypto import KeyManager


class MainWindow:
    """主窗口类"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("匿名加密通讯系统")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)

        # 应用状态
        self.client: Optional[AnonymousClient] = None
        self.key_manager: Optional[KeyManager] = None
        self.user_id: str = ""
        self.is_connected = False
        self.contacts: Dict[str, Dict] = {}
        self.current_chat_contact: Optional[str] = None

        # 异步事件循环
        self.loop = None
        self.loop_thread = None

        # 创建界面
        self._create_widgets()
        self._setup_layout()
        self._setup_bindings()

        # 加载配置
        self._load_config()

        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

    def _create_widgets(self):
        """创建界面组件"""
        # 创建菜单栏
        self._create_menu()

        # 创建工具栏
        self._create_toolbar()

        # 创建主面板
        self._create_main_panel()

        # 创建状态栏
        self._create_status_bar()

    def _create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="连接", command=self._connect)
        file_menu.add_command(label="断开连接", command=self._disconnect)
        file_menu.add_separator()
        file_menu.add_command(label="导入密钥", command=self._import_keys)
        file_menu.add_command(label="导出密钥", command=self._export_keys)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing)

        # 联系人菜单
        contact_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="联系人", menu=contact_menu)
        contact_menu.add_command(label="添加联系人", command=self._add_contact)
        contact_menu.add_command(label="删除联系人", command=self._remove_contact)
        contact_menu.add_command(label="联系人信息", command=self._contact_info)

        # 安全菜单
        security_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="安全", menu=security_menu)
        security_menu.add_command(label="更换身份", command=self._rotate_identity)
        security_menu.add_command(label="密钥管理", command=self._key_management)
        security_menu.add_command(label="安全设置", command=self._security_settings)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)

    def _create_toolbar(self):
        """创建工具栏"""
        self.toolbar = ttk.Frame(self.root)

        # 连接按钮
        self.connect_btn = ttk.Button(
            self.toolbar, text="连接", command=self._connect
        )
        self.connect_btn.pack(side=tk.LEFT, padx=2)

        # 断开连接按钮
        self.disconnect_btn = ttk.Button(
            self.toolbar, text="断开", command=self._disconnect, state=tk.DISABLED
        )
        self.disconnect_btn.pack(side=tk.LEFT, padx=2)

        # 分隔符
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 添加联系人按钮
        ttk.Button(
            self.toolbar, text="添加联系人", command=self._add_contact
        ).pack(side=tk.LEFT, padx=2)

        # 发送文件按钮
        self.send_file_btn = ttk.Button(
            self.toolbar, text="发送文件", command=self._send_file, state=tk.DISABLED
        )
        self.send_file_btn.pack(side=tk.LEFT, padx=2)

        # 分隔符
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 身份标签
        ttk.Label(self.toolbar, text="身份:").pack(side=tk.LEFT, padx=2)
        self.identity_label = ttk.Label(self.toolbar, text="未连接", foreground="red")
        self.identity_label.pack(side=tk.LEFT, padx=2)

    def _create_main_panel(self):
        """创建主面板"""
        # 创建水平分割面板
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)

        # 左侧面板 - 联系人列表
        self._create_contact_panel()

        # 右侧面板 - 聊天区域
        self._create_chat_panel()

        self.main_paned.add(self.contact_frame, weight=1)
        self.main_paned.add(self.chat_frame, weight=3)

    def _create_contact_panel(self):
        """创建联系人面板"""
        self.contact_frame = ttk.Frame(self.main_paned)

        # 联系人列表标题
        ttk.Label(self.contact_frame, text="联系人", font=("Arial", 12, "bold")).pack(pady=5)

        # 联系人列表
        self.contact_listbox = tk.Listbox(
            self.contact_frame,
            selectmode=tk.SINGLE,
            font=("Arial", 10)
        )
        self.contact_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.contact_listbox.bind('<<ListboxSelect>>', self._on_contact_select)

        # 联系人操作按钮
        contact_btn_frame = ttk.Frame(self.contact_frame)
        contact_btn_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(
            contact_btn_frame, text="添加", command=self._add_contact
        ).pack(side=tk.LEFT, padx=2)

        ttk.Button(
            contact_btn_frame, text="删除", command=self._remove_contact
        ).pack(side=tk.LEFT, padx=2)

        ttk.Button(
            contact_btn_frame, text="信息", command=self._contact_info
        ).pack(side=tk.LEFT, padx=2)

    def _create_chat_panel(self):
        """创建聊天面板"""
        self.chat_frame = ttk.Frame(self.main_paned)

        # 聊天标题
        self.chat_title = ttk.Label(
            self.chat_frame, text="选择联系人开始聊天",
            font=("Arial", 12, "bold")
        )
        self.chat_title.pack(pady=5)

        # 消息显示区域
        self.message_frame = ttk.Frame(self.chat_frame)
        self.message_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.message_display = scrolledtext.ScrolledText(
            self.message_frame,
            state=tk.DISABLED,
            wrap=tk.WORD,
            font=("Arial", 10),
            bg="#f0f0f0"
        )
        self.message_display.pack(fill=tk.BOTH, expand=True)

        # 消息输入区域
        self.input_frame = ttk.Frame(self.chat_frame)
        self.input_frame.pack(fill=tk.X, padx=5, pady=5)

        # 消息输入框
        self.message_entry = tk.Text(
            self.input_frame,
            height=3,
            wrap=tk.WORD,
            font=("Arial", 10)
        )
        self.message_entry.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

        # 发送按钮
        self.send_btn = ttk.Button(
            self.input_frame, text="发送", command=self._send_message, state=tk.DISABLED
        )
        self.send_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 绑定回车键发送消息
        self.message_entry.bind('<Control-Return>', lambda e: self._send_message())

    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)

        # 连接状态
        self.connection_status = ttk.Label(
            self.status_bar, text="未连接", foreground="red"
        )
        self.connection_status.pack(side=tk.LEFT, padx=5)

        # 分隔符
        ttk.Separator(self.status_bar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 消息计数
        self.message_count = ttk.Label(self.status_bar, text="消息: 0")
        self.message_count.pack(side=tk.LEFT, padx=5)

        # 分隔符
        ttk.Separator(self.status_bar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 加密状态
        self.encryption_status = ttk.Label(
            self.status_bar, text="加密: AES-256", foreground="green"
        )
        self.encryption_status.pack(side=tk.LEFT, padx=5)

    def _setup_layout(self):
        """设置布局"""
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=2, pady=2)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=2, pady=2)

    def _setup_bindings(self):
        """设置事件绑定"""
        # 窗口大小改变事件
        self.root.bind('<Configure>', self._on_window_configure)

        # 键盘快捷键
        self.root.bind('<Control-q>', lambda e: self._on_closing())
        self.root.bind('<Control-n>', lambda e: self._add_contact())
        self.root.bind('<F5>', lambda e: self._connect())

    def _load_config(self):
        """加载配置"""
        try:
            config_path = "config/client_config.json"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.user_id = config.get('user_id', '')
                    self.contacts = config.get('contacts', {})
                    self._update_contact_list()
        except Exception as e:
            print(f"加载配置失败: {e}")

    def _save_config(self):
        """保存配置"""
        try:
            os.makedirs("config", exist_ok=True)
            config = {
                'user_id': self.user_id,
                'contacts': self.contacts
            }
            with open("config/client_config.json", 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")

    def _update_contact_list(self):
        """更新联系人列表"""
        self.contact_listbox.delete(0, tk.END)
        for contact_id, contact_info in self.contacts.items():
            display_name = contact_info.get('name', contact_id)
            status = "在线" if contact_info.get('online', False) else "离线"
            self.contact_listbox.insert(tk.END, f"{display_name} ({status})")

    def _on_contact_select(self, event):
        """联系人选择事件"""
        selection = self.contact_listbox.curselection()
        if selection:
            index = selection[0]
            contact_ids = list(self.contacts.keys())
            if index < len(contact_ids):
                self.current_chat_contact = contact_ids[index]
                contact_info = self.contacts[self.current_chat_contact]
                self.chat_title.config(text=f"与 {contact_info.get('name', self.current_chat_contact)} 聊天")
                self._load_chat_history()
                self.send_btn.config(state=tk.NORMAL if self.is_connected else tk.DISABLED)
                self.send_file_btn.config(state=tk.NORMAL if self.is_connected else tk.DISABLED)

    def _load_chat_history(self):
        """加载聊天历史"""
        if not self.current_chat_contact:
            return

        self.message_display.config(state=tk.NORMAL)
        self.message_display.delete(1.0, tk.END)

        # 从联系人信息中加载历史消息
        contact_info = self.contacts.get(self.current_chat_contact, {})
        messages = contact_info.get('messages', [])

        for message in messages:
            self._display_message(
                message['sender'],
                message['content'],
                message['timestamp'],
                message.get('type', 'text')
            )

        self.message_display.config(state=tk.DISABLED)
        self.message_display.see(tk.END)

    def _display_message(self, sender: str, content: str, timestamp: str, msg_type: str = 'text'):
        """显示消息"""
        self.message_display.config(state=tk.NORMAL)

        # 格式化时间戳
        try:
            dt = datetime.fromisoformat(timestamp)
            time_str = dt.strftime("%H:%M:%S")
        except:
            time_str = timestamp

        # 确定发送者显示名称
        if sender == self.user_id:
            sender_name = "我"
            tag = "sent"
        else:
            contact_info = self.contacts.get(sender, {})
            sender_name = contact_info.get('name', sender)
            tag = "received"

        # 插入消息
        if msg_type == 'file':
            message_text = f"[{time_str}] {sender_name}: [文件] {content}\n"
        else:
            message_text = f"[{time_str}] {sender_name}: {content}\n"

        self.message_display.insert(tk.END, message_text, tag)

        # 配置标签样式
        self.message_display.tag_config("sent", foreground="blue")
        self.message_display.tag_config("received", foreground="green")

        self.message_display.config(state=tk.DISABLED)
        self.message_display.see(tk.END)

    # 网络操作方法
    def _connect(self):
        """连接到网络"""
        if self.is_connected:
            return

        # 总是弹出身份设置对话框（除非已经设置过）
        if not self.user_id or not hasattr(self, 'display_name'):
            print("弹出身份设置对话框...")
            self.user_id = self._get_user_id()
            if not self.user_id:
                print("用户取消了身份设置")
                return
            print(f"✓ 用户身份已设置: {self.user_id}")
        else:
            print(f"使用已有身份: {self.user_id}")

        try:
            # 立即更新UI状态显示正在连接
            self.connection_status.config(text="正在连接...", foreground="orange")
            self.connect_btn.config(state=tk.DISABLED)
            self.root.update()  # 强制更新UI

            print(f"开始连接，用户ID: {self.user_id}")

            # 启动异步事件循环
            self._start_async_loop()

            # 等待事件循环启动并验证
            import time
            max_wait = 10  # 最多等待1秒
            for i in range(max_wait):
                time.sleep(0.1)
                if self.loop and hasattr(self.loop, 'is_running') and self.loop.is_running():
                    break
                if i == max_wait - 1:
                    raise Exception("事件循环启动超时")

            print("✓ 异步事件循环已启动")

            # 创建客户端
            relay_servers = DEFAULT_NETWORK_CONFIG['relay_servers']
            print(f"使用中继服务器: {relay_servers}")

            self.client = AnonymousClient(self.user_id, relay_servers)
            print("✓ 客户端创建成功")

            # 获取事件循环引用
            self.event_loop = self.loop
            print(f"✓ 事件循环引用已设置: {self.event_loop}")

            # 消息处理器将在连接完成后设置
            print("✓ 客户端初始化完成")

            # 异步启动客户端
            if self.loop and self.loop.is_running():
                print("提交异步连接任务...")
                future = asyncio.run_coroutine_threadsafe(self._async_connect(), self.loop)

                # 设置连接状态检查
                def check_connection():
                    try:
                        if future.done():
                            if future.exception():
                                print(f"连接异常: {future.exception()}")
                                self.root.after(0, lambda: messagebox.showerror("连接错误", f"连接失败: {future.exception()}"))
                            else:
                                print("连接任务完成，检查连接状态...")
                                # 连接任务完成，强制检查状态
                                self.root.after(100, self._force_update_ui)
                            return
                        # 如果还没完成，继续检查
                        self.root.after(100, check_connection)
                    except Exception as e:
                        print(f"连接检查失败: {e}")
                        self.root.after(0, lambda: messagebox.showerror("连接错误", f"连接检查失败: {e}"))

                check_connection()
            else:
                messagebox.showerror("连接错误", "异步事件循环未启动")

        except Exception as e:
            print(f"连接过程中出错: {e}")
            messagebox.showerror("连接错误", f"连接失败: {e}")
            # 清理资源
            self._cleanup_connection()

    def _disconnect(self):
        """断开连接"""
        if not self.is_connected:
            return

        try:
            if self.client:
                asyncio.run_coroutine_threadsafe(self.client.stop(), self.loop)

            self._stop_async_loop()

            self.is_connected = False
            self._update_connection_status()

        except Exception as e:
            messagebox.showerror("断开连接错误", f"断开连接失败: {e}")

    async def _async_connect(self):
        """异步连接 - 适配复杂握手协议"""
        connection_stages = [
            ("正在连接中继服务器...", 0),
            ("正在建立安全连接...", 20),
            ("正在注册用户身份...", 40),
            ("正在初始化加密协议...", 60),
            ("正在建立匿名电路...", 80),
            ("连接完成", 100)
        ]

        current_stage = 0

        def update_progress(stage_index, extra_info=""):
            if stage_index < len(connection_stages):
                stage_text, progress = connection_stages[stage_index]
                full_text = f"{stage_text} {extra_info}".strip()
                self.root.after(0, lambda: self.connection_status.config(text=full_text, foreground="orange"))
                print(f"连接阶段 {stage_index + 1}/{len(connection_stages)}: {full_text}")

        try:
            print("开始复杂握手协议连接...")

            # 阶段1: 连接中继服务器
            update_progress(0)
            await asyncio.sleep(0.5)  # 给UI时间更新

            # 启动客户端（这会连接到中继服务器）
            # 不等待start()完成，让它在后台运行
            client_start_task = asyncio.create_task(self.client.start())

            # 阶段2: 建立安全连接
            update_progress(1)
            await asyncio.sleep(2.0)  # 给更多时间建立连接

            # 监控连接进度
            max_wait_time = 30  # 最多等待30秒
            check_interval = 0.5
            elapsed_time = 0

            while elapsed_time < max_wait_time:
                # 检查WebSocket连接状态
                if len(self.client.connections) > 0:
                    update_progress(2, f"({len(self.client.connections)}/3 服务器)")
                    break

                await asyncio.sleep(check_interval)
                elapsed_time += check_interval

                if elapsed_time > 5:  # 5秒后还没连接，显示等待信息
                    update_progress(1, f"(等待 {elapsed_time:.1f}s)")

            if len(self.client.connections) == 0:
                raise Exception("无法连接到中继服务器")

            # 阶段3: 等待用户注册
            update_progress(2, f"({len(self.client.connections)} 服务器已连接)")

            # 等待注册完成
            registration_wait = 0
            max_registration_wait = 10

            while registration_wait < max_registration_wait:
                if self.client.is_registered:
                    break
                await asyncio.sleep(0.5)
                registration_wait += 0.5
                update_progress(2, f"(注册中... {registration_wait:.1f}s)")

            # 阶段4: 初始化加密协议
            update_progress(3)
            await asyncio.sleep(1.0)

            # 检查协议初始化
            if hasattr(self.client, 'protocol') and self.client.protocol:
                update_progress(3, "(协议已初始化)")

            # 阶段5: 建立匿名电路
            update_progress(4)
            await asyncio.sleep(1.0)

            # 检查洋葱路由器
            if hasattr(self.client, 'onion_router') and self.client.onion_router:
                update_progress(4, "(电路已准备)")

            # 最终检查连接状态
            final_check_passed = False

            if self.client.is_connected():
                final_check_passed = True
                print("✓ 完整连接检查通过")
            elif len(self.client.connections) > 0 and self.client.running:
                final_check_passed = True
                print("✓ 基本连接检查通过")
            elif len(self.client.connections) > 0:
                # 即使注册状态不明确，如果有连接就认为成功
                final_check_passed = True
                print("✓ 连接存在，认为连接成功")

            if not final_check_passed:
                raise Exception("连接验证失败")

            # 阶段6: 连接完成
            update_progress(5)
            self.is_connected = True

            # 不等待客户端任务完成，让它在后台持续运行
            # 客户端任务会保持连接活跃
            print("客户端任务在后台运行，连接保持活跃")

            print(f"✓ 复杂握手协议连接完成: is_connected = {self.is_connected}")

            # 设置消息处理器
            print("设置消息处理器...")
            self._setup_message_handlers()
            print("✓ 消息处理器设置完成")

            # 更新UI为成功状态
            def update_ui_success():
                try:
                    print("更新UI为连接成功状态...")
                    self.connection_status.config(text="已连接", foreground="green")
                    self.identity_label.config(text=f"{self.user_id} ({getattr(self, 'display_name', self.user_id)})", foreground="blue")
                    self.connect_btn.config(state=tk.DISABLED)
                    self.disconnect_btn.config(state=tk.NORMAL)

                    # 启用聊天功能
                    if self.current_chat_contact:
                        self.send_btn.config(state=tk.NORMAL)
                        self.send_file_btn.config(state=tk.NORMAL)

                    # 强制刷新界面
                    self.root.update_idletasks()
                    self.root.update()

                    print("✓ UI状态更新完成")

                    # 显示成功消息
                    success_msg = f"已成功连接到匿名网络\n\n用户ID: {self.user_id}"
                    if hasattr(self, 'display_name') and self.display_name != self.user_id:
                        success_msg += f"\n显示名称: {self.display_name}"
                    success_msg += f"\n连接的服务器: {len(self.client.connections)}"

                    self.root.after(500, lambda: messagebox.showinfo("连接成功", success_msg))

                except Exception as ui_error:
                    print(f"UI更新失败: {ui_error}")

            # 立即执行UI更新
            self.root.after(0, update_ui_success)

        except Exception as e:
            print(f"复杂握手协议连接失败: {e}")
            self.is_connected = False

            def update_ui_failure(error_msg=str(e)):
                self.connection_status.config(text="连接失败", foreground="red")
                self.connect_btn.config(state=tk.NORMAL)
                error_detail = f"连接失败: {error_msg}\n\n可能的原因:\n• 中继服务器未启动\n• 网络连接问题\n• 握手协议超时"
                messagebox.showerror("连接错误", error_detail)

            self.root.after(0, update_ui_failure)

    def _start_async_loop(self):
        """启动异步事件循环"""
        if self.loop_thread and self.loop_thread.is_alive():
            return

        def run_loop():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_forever()

        self.loop_thread = threading.Thread(target=run_loop, daemon=True)
        self.loop_thread.start()

    def _stop_async_loop(self):
        """停止异步事件循环"""
        if self.loop:
            self.loop.call_soon_threadsafe(self.loop.stop)
            self.loop = None

        if self.loop_thread:
            self.loop_thread.join(timeout=1)
            self.loop_thread = None

    def _cleanup_connection(self):
        """清理连接资源"""
        try:
            self.is_connected = False
            self.client = None
            self._stop_async_loop()
            self._update_connection_status()
        except Exception as e:
            print(f"清理连接资源时出错: {e}")

    def _setup_message_handlers(self):
        """设置消息处理器"""
        if not self.client:
            return

        # 设置消息处理器
        self.client.relay_message_handlers['message_received'] = self._handle_message_received
        self.client.relay_message_handlers['file_received'] = self._handle_file_received
        self.client.relay_message_handlers['user_status_update'] = self._handle_user_status_update

        # 重写客户端的消息处理方法
        original_handle_text = self.client._handle_text_message
        original_handle_file_init = self.client._handle_file_transfer_init
        original_handle_file_chunk = self.client._handle_file_chunk

        async def handle_text_message(message):
            await original_handle_text(message)
            # 在UI中显示消息
            try:
                decrypted_text = self.client.protocol.decrypt_text_message(message)
                timestamp = datetime.now().isoformat()

                # 保存消息到联系人历史
                sender_id = message.header.sender_id
                if sender_id not in self.contacts:
                    self.contacts[sender_id] = {'name': sender_id, 'messages': []}

                self.contacts[sender_id]['messages'].append({
                    'sender': sender_id,
                    'content': decrypted_text,
                    'timestamp': timestamp,
                    'type': 'text'
                })

                # 更新UI
                self.root.after(0, lambda: self._on_message_received(sender_id, decrypted_text, timestamp))

            except Exception as e:
                print(f"处理文本消息UI更新失败: {e}")

        async def handle_file_init(message):
            await original_handle_file_init(message)
            # 处理文件传输初始化
            filename = message.payload['filename']
            file_size = message.payload['file_size']
            sender_id = message.header.sender_id

            self.root.after(0, lambda: self._on_file_transfer_init(sender_id, filename, file_size))

        # 替换处理器
        self.client._handle_text_message = handle_text_message
        self.client._handle_file_transfer_init = handle_file_init

    def _update_connection_status(self):
        """更新连接状态"""
        print(f"更新连接状态: is_connected={self.is_connected}")
        if self.is_connected:
            self.connection_status.config(text="已连接", foreground="green")
            self.identity_label.config(text=self.user_id, foreground="blue")
            self.connect_btn.config(state=tk.DISABLED)
            self.disconnect_btn.config(state=tk.NORMAL)

            if self.current_chat_contact:
                self.send_btn.config(state=tk.NORMAL)
                self.send_file_btn.config(state=tk.NORMAL)
        else:
            self.connection_status.config(text="未连接", foreground="red")
            self.identity_label.config(text="未连接", foreground="red")
            self.connect_btn.config(state=tk.NORMAL)
            self.disconnect_btn.config(state=tk.DISABLED)
            self.send_btn.config(state=tk.DISABLED)
            self.send_file_btn.config(state=tk.DISABLED)

    def _force_update_ui(self):
        """强制更新UI状态"""
        print("强制更新UI状态...")
        try:
            # 强制刷新连接状态
            self._update_connection_status()
            self.root.update_idletasks()
            self.root.update()
            print("✓ UI强制更新完成")

            # 如果连接成功，显示成功消息
            if self.is_connected:
                messagebox.showinfo("连接成功", f"已成功连接到匿名网络\n用户ID: {self.user_id}")
        except Exception as e:
            print(f"强制更新UI失败: {e}")

    def _verify_ui_state(self):
        """验证UI状态是否正确"""
        try:
            print(f"验证UI状态: is_connected={self.is_connected}")
            current_status = self.connection_status.cget('text')
            print(f"当前显示状态: {current_status}")

            if self.is_connected and current_status != "已连接":
                print("⚠ UI状态不一致，强制修复...")
                self.connection_status.config(text="已连接", foreground="green")
                self.identity_label.config(text=self.user_id, foreground="blue")
                self.connect_btn.config(state=tk.DISABLED)
                self.disconnect_btn.config(state=tk.NORMAL)
                self.root.update_idletasks()
                self.root.update()
                print("✓ UI状态已修复")
            elif self.is_connected:
                print("✓ UI状态正确")
            else:
                print("连接状态为False，UI显示正确")

        except Exception as e:
            print(f"验证UI状态失败: {e}")

    def _get_user_id(self):
        """获取用户ID"""
        dialog = UserIdDialog(self.root)
        self.root.wait_window(dialog.dialog)
        if dialog.result:
            self.display_name = dialog.display_name
            print(f"✓ 获取用户身份: ID={dialog.result}, 显示名称={self.display_name}")
        return dialog.result

    # 消息处理方法
    def _send_message(self):
        """发送消息"""
        if not self.is_connected or not self.current_chat_contact:
            return

        message_text = self.message_entry.get(1.0, tk.END).strip()
        if not message_text:
            return

        try:
            # 异步发送消息
            print(f"发送消息到 {self.current_chat_contact}: {message_text}")
            asyncio.run_coroutine_threadsafe(
                self.client.send_simple_message(self.current_chat_contact, message_text),
                self.loop
            )

            # 清空输入框
            self.message_entry.delete(1.0, tk.END)

            # 在UI中显示发送的消息
            timestamp = datetime.now().isoformat()
            self._display_message(self.user_id, message_text, timestamp)

            # 保存到联系人历史
            if self.current_chat_contact not in self.contacts:
                self.contacts[self.current_chat_contact] = {'name': self.current_chat_contact, 'messages': []}

            self.contacts[self.current_chat_contact]['messages'].append({
                'sender': self.user_id,
                'content': message_text,
                'timestamp': timestamp,
                'type': 'text'
            })

            self._save_config()

        except Exception as e:
            messagebox.showerror("发送错误", f"发送消息失败: {e}")

    def _send_file(self):
        """发送文件"""
        if not self.is_connected or not self.current_chat_contact:
            return

        file_path = filedialog.askopenfilename(
            title="选择要发送的文件",
            filetypes=[("所有文件", "*.*")]
        )

        if not file_path:
            return

        try:
            # 异步发送文件
            asyncio.run_coroutine_threadsafe(
                self.client.send_file(self.current_chat_contact, file_path),
                self.loop
            )

            # 在UI中显示文件发送信息
            filename = os.path.basename(file_path)
            timestamp = datetime.now().isoformat()
            self._display_message(self.user_id, filename, timestamp, 'file')

            # 保存到联系人历史
            if self.current_chat_contact not in self.contacts:
                self.contacts[self.current_chat_contact] = {'name': self.current_chat_contact, 'messages': []}

            self.contacts[self.current_chat_contact]['messages'].append({
                'sender': self.user_id,
                'content': filename,
                'timestamp': timestamp,
                'type': 'file'
            })

            self._save_config()

        except Exception as e:
            messagebox.showerror("发送文件错误", f"发送文件失败: {e}")

    def _on_message_received(self, sender_id: str, content: str, timestamp: str):
        """收到消息时的处理"""
        # 如果当前正在与发送者聊天，显示消息
        if self.current_chat_contact == sender_id:
            self._display_message(sender_id, content, timestamp)

        # 更新联系人列表（显示新消息提示）
        self._update_contact_list()
        self._save_config()

    def _on_file_transfer_init(self, sender_id: str, filename: str, file_size: int):
        """文件传输初始化处理"""
        result = messagebox.askyesno(
            "文件传输",
            f"{sender_id} 要发送文件: {filename} ({file_size} 字节)\n是否接受？"
        )

        if result:
            # 选择保存位置
            save_path = filedialog.asksaveasfilename(
                title="保存文件",
                initialvalue=filename
            )

            if save_path:
                # 这里应该实现文件接收逻辑
                messagebox.showinfo("文件传输", f"文件将保存到: {save_path}")

    # 联系人管理方法
    def _add_contact(self):
        """添加联系人"""
        if not self.is_connected:
            messagebox.showwarning("未连接", "请先连接到网络")
            return

        dialog = AddContactDialog(self.root)
        self.root.wait_window(dialog.dialog)

        if dialog.result:
            contact_id, contact_name = dialog.result
            self.contacts[contact_id] = {
                'name': contact_name,
                'messages': [],
                'online': False,
                'status': 'checking'  # 初始状态为检查中
            }
            self._update_contact_list()
            self._save_config()

            # 延迟查询联系人状态，确保对方有时间注册
            def delayed_check():
                self._schedule_async_task(self._check_contact_status(contact_id))

            # 3秒后查询状态
            self.root.after(3000, delayed_check)

            messagebox.showinfo("成功", f"已添加联系人: {contact_name} ({contact_id})\n正在检查在线状态...")

    async def _check_contact_status(self, contact_id):
        """检查联系人在线状态"""
        try:
            print(f"检查联系人状态: {contact_id}")

            if self.client and hasattr(self.client, 'lookup_user_status'):
                # 使用客户端的用户查找功能
                is_online = await self.client.lookup_user_status(contact_id)

                # 更新联系人状态
                if contact_id in self.contacts:
                    self.contacts[contact_id]['online'] = is_online
                    self.contacts[contact_id]['status'] = 'online' if is_online else 'offline'

                    # 在主线程中更新UI
                    self.root.after(0, self._update_contact_list)

                    status_text = "在线" if is_online else "离线"
                    print(f"✓ 联系人 {contact_id} 状态: {status_text}")
                else:
                    print(f"联系人 {contact_id} 不在联系人列表中")
            else:
                print("客户端不支持用户状态查询")
                # 设置为离线状态
                if contact_id in self.contacts:
                    self.contacts[contact_id]['online'] = False
                    self.contacts[contact_id]['status'] = 'offline'
                    self.root.after(0, self._update_contact_list)

        except Exception as e:
            print(f"检查联系人状态失败: {e}")
            # 设置为离线状态
            if contact_id in self.contacts:
                self.contacts[contact_id]['online'] = False
                self.contacts[contact_id]['status'] = 'offline'
                self.root.after(0, self._update_contact_list)

    def _schedule_async_task(self, coro):
        """线程安全地调度异步任务"""
        def run_async():
            try:
                # 检查事件循环是否可用
                if hasattr(self, 'event_loop') and self.event_loop and not self.event_loop.is_closed():
                    print(f"调度异步任务到事件循环: {self.event_loop}")
                    # 使用call_soon_threadsafe在事件循环中调度任务
                    future = asyncio.run_coroutine_threadsafe(coro, self.event_loop)

                    # 等待任务完成并处理结果
                    def handle_result():
                        try:
                            result = future.result(timeout=10)  # 10秒超时
                            print(f"异步任务完成: {result}")
                        except asyncio.TimeoutError:
                            print("异步任务超时")
                        except Exception as e:
                            print(f"异步任务执行失败: {e}")

                    # 在新线程中等待结果
                    threading.Thread(target=handle_result, daemon=True).start()

                elif hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
                    print(f"使用备用事件循环: {self.loop}")
                    # 使用备用事件循环
                    future = asyncio.run_coroutine_threadsafe(coro, self.loop)

                    def handle_result():
                        try:
                            result = future.result(timeout=10)
                            print(f"异步任务完成: {result}")
                        except Exception as e:
                            print(f"异步任务执行失败: {e}")

                    threading.Thread(target=handle_result, daemon=True).start()
                else:
                    print("事件循环不可用，无法调度异步任务")
                    # 关闭协程以避免警告
                    coro.close()

            except Exception as e:
                print(f"调度异步任务失败: {e}")
                # 关闭协程以避免警告
                try:
                    coro.close()
                except:
                    pass

        # 在新线程中运行
        threading.Thread(target=run_async, daemon=True).start()

    async def _handle_message_received(self, data):
        """处理接收到的消息"""
        try:
            sender_user_id = data.get('sender_user_id')
            encrypted_content = data.get('encrypted_content')
            timestamp = data.get('timestamp')

            print(f"GUI收到消息来自 {sender_user_id}: {encrypted_content}")

            # 在主线程中更新GUI
            def update_gui():
                try:
                    # 查找发送者联系人
                    sender_name = sender_user_id
                    if sender_user_id in self.contacts:
                        sender_name = self.contacts[sender_user_id]['display_name']

                    # 添加消息到聊天窗口
                    if self.current_chat_contact == sender_user_id:
                        self._display_message(sender_name, encrypted_content, str(timestamp), is_sent=False)

                    # 更新联系人列表（显示新消息提示）
                    if sender_user_id in self.contacts:
                        self.contacts[sender_user_id]['last_message'] = encrypted_content[:30] + "..." if len(encrypted_content) > 30 else encrypted_content
                        self.contacts[sender_user_id]['unread_count'] = self.contacts[sender_user_id].get('unread_count', 0) + 1
                        self._update_contact_list()

                    print(f"✓ GUI消息处理完成: {sender_user_id}")

                except Exception as e:
                    print(f"GUI消息处理失败: {e}")

            # 在主线程中执行GUI更新
            self.root.after(0, update_gui)

        except Exception as e:
            print(f"处理接收消息失败: {e}")

    async def _handle_file_received(self, data):
        """处理接收到的文件"""
        try:
            sender_user_id = data.get('sender_user_id')
            filename = data.get('filename')
            file_size = data.get('file_size', 0)

            print(f"GUI收到文件来自 {sender_user_id}: {filename} ({file_size} bytes)")

            # 在主线程中更新GUI
            def update_gui():
                try:
                    # 查找发送者联系人
                    sender_name = sender_user_id
                    if sender_user_id in self.contacts:
                        sender_name = self.contacts[sender_user_id]['display_name']

                    # 添加文件消息到聊天窗口
                    file_message = f"📁 文件: {filename} ({file_size} bytes)"
                    if self.current_chat_contact == sender_user_id:
                        self._add_message_to_chat(sender_name, file_message, False)

                    print(f"✓ GUI文件处理完成: {sender_user_id}")

                except Exception as e:
                    print(f"GUI文件处理失败: {e}")

            # 在主线程中执行GUI更新
            self.root.after(0, update_gui)

        except Exception as e:
            print(f"处理接收文件失败: {e}")

    async def _handle_user_status_update(self, data):
        """处理用户状态更新"""
        try:
            user_id = data.get('user_id')
            is_online = data.get('is_online', False)

            print(f"用户状态更新: {user_id} -> {'在线' if is_online else '离线'}")

            # 在主线程中更新GUI
            def update_gui():
                try:
                    if user_id in self.contacts:
                        self.contacts[user_id]['online'] = is_online
                        self.contacts[user_id]['status'] = 'online' if is_online else 'offline'
                        self._update_contact_list()
                        print(f"✓ 联系人状态已更新: {user_id}")
                except Exception as e:
                    print(f"更新联系人状态失败: {e}")

            # 在主线程中执行GUI更新
            self.root.after(0, update_gui)

        except Exception as e:
            print(f"处理用户状态更新失败: {e}")

    def _display_message(self, sender_name: str, content: str, timestamp: str, is_sent: bool = True, msg_type: str = 'text'):
        """在聊天窗口显示消息"""
        try:
            # 格式化时间戳
            try:
                from datetime import datetime
                if isinstance(timestamp, str):
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime("%H:%M:%S")
                else:
                    time_str = str(timestamp)
            except:
                time_str = str(timestamp)

            # 创建消息文本
            if is_sent:
                message_text = f"[{time_str}] 我: {content}\n"
                tag = "sent"
            else:
                message_text = f"[{time_str}] {sender_name}: {content}\n"
                tag = "received"

            # 添加到聊天显示区域
            self.message_display.config(state=tk.NORMAL)
            self.message_display.insert(tk.END, message_text, tag)
            self.message_display.config(state=tk.DISABLED)

            # 滚动到底部
            self.message_display.see(tk.END)

            print(f"✓ 消息已显示: {sender_name} -> {content[:30]}...")

        except Exception as e:
            print(f"显示消息失败: {e}")

    def _remove_contact(self):
        """删除联系人"""
        if not self.current_chat_contact:
            messagebox.showwarning("删除联系人", "请先选择要删除的联系人")
            return

        contact_info = self.contacts[self.current_chat_contact]
        result = messagebox.askyesno(
            "删除联系人",
            f"确定要删除联系人 {contact_info.get('name', self.current_chat_contact)} 吗？"
        )

        if result:
            del self.contacts[self.current_chat_contact]
            self.current_chat_contact = None
            self.chat_title.config(text="选择联系人开始聊天")
            self.message_display.config(state=tk.NORMAL)
            self.message_display.delete(1.0, tk.END)
            self.message_display.config(state=tk.DISABLED)
            self.send_btn.config(state=tk.DISABLED)
            self.send_file_btn.config(state=tk.DISABLED)
            self._update_contact_list()
            self._save_config()

    def _contact_info(self):
        """显示联系人信息"""
        if not self.current_chat_contact:
            messagebox.showwarning("联系人信息", "请先选择联系人")
            return

        contact_info = self.contacts[self.current_chat_contact]
        message_count = len(contact_info.get('messages', []))

        info_text = f"""
联系人ID: {self.current_chat_contact}
显示名称: {contact_info.get('name', '未设置')}
在线状态: {'在线' if contact_info.get('online', False) else '离线'}
消息数量: {message_count}
        """

        messagebox.showinfo("联系人信息", info_text.strip())

    # 安全功能方法
    def _rotate_identity(self):
        """更换身份"""
        if not self.is_connected or not self.client:
            messagebox.showwarning("更换身份", "请先连接到网络")
            return

        result = messagebox.askyesno("更换身份", "确定要更换匿名身份吗？这将断开当前所有连接。")
        if result:
            try:
                # 轮换身份
                new_identity = self.client.identity_manager.rotate_identity()
                self.identity_label.config(text=new_identity)
                messagebox.showinfo("更换身份", f"身份已更换为: {new_identity}")
            except Exception as e:
                messagebox.showerror("更换身份错误", f"更换身份失败: {e}")

    def _key_management(self):
        """密钥管理"""
        dialog = KeyManagementDialog(self.root, self.key_manager)
        self.root.wait_window(dialog.dialog)

    def _security_settings(self):
        """安全设置"""
        dialog = SecuritySettingsDialog(self.root)
        self.root.wait_window(dialog.dialog)

    def _import_keys(self):
        """导入密钥"""
        file_path = filedialog.askopenfilename(
            title="选择密钥文件",
            filetypes=[("密钥文件", "*.key"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 这里应该实现密钥导入逻辑
                messagebox.showinfo("导入密钥", "密钥导入功能待实现")
            except Exception as e:
                messagebox.showerror("导入密钥错误", f"导入密钥失败: {e}")

    def _export_keys(self):
        """导出密钥"""
        file_path = filedialog.asksaveasfilename(
            title="保存密钥文件",
            defaultextension=".key",
            filetypes=[("密钥文件", "*.key"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 这里应该实现密钥导出逻辑
                messagebox.showinfo("导出密钥", "密钥导出功能待实现")
            except Exception as e:
                messagebox.showerror("导出密钥错误", f"导出密钥失败: {e}")

    # 帮助和信息方法
    def _show_help(self):
        """显示帮助"""
        help_text = """
匿名加密通讯系统使用说明

1. 连接网络
   - 点击"连接"按钮或按F5连接到匿名网络
   - 首次使用需要输入用户ID

2. 添加联系人
   - 点击"添加联系人"按钮或按Ctrl+N
   - 输入联系人ID和显示名称

3. 发送消息
   - 选择联系人后在输入框中输入消息
   - 按Ctrl+Enter或点击"发送"按钮发送

4. 发送文件
   - 选择联系人后点击"发送文件"按钮
   - 选择要发送的文件

5. 安全功能
   - 定期更换身份以增强匿名性
   - 管理加密密钥
   - 配置安全设置

快捷键：
- Ctrl+Q: 退出程序
- Ctrl+N: 添加联系人
- F5: 连接网络
- Ctrl+Enter: 发送消息
        """

        HelpDialog(self.root, help_text)

    def _show_about(self):
        """显示关于信息"""
        about_text = """
匿名加密通讯系统 v1.0

一个基于多层加密和洋葱路由的匿名通讯系统

特性：
• 端到端加密
• 洋葱路由匿名化
• 完美前向保密
• 流量混淆
• 身份轮换

技术栈：
• Python 3.9+
• cryptography库
• asyncio异步编程
• tkinter GUI
• WebSocket通信

开发团队：匿名加密通讯系统团队
版权所有 © 2024
        """

        AboutDialog(self.root, about_text)

    # 事件处理方法
    def _on_window_configure(self, event):
        """窗口配置改变事件"""
        if event.widget == self.root:
            # 可以在这里处理窗口大小改变
            pass

    def _on_closing(self):
        """窗口关闭事件"""
        if self.is_connected:
            result = messagebox.askyesno("退出", "当前已连接到网络，确定要退出吗？")
            if not result:
                return

        # 保存配置
        self._save_config()

        # 断开连接
        if self.is_connected:
            self._disconnect()

        # 关闭窗口
        self.root.destroy()

    def run(self):
        """运行应用"""
        self.root.mainloop()


# 对话框类
class UserIdDialog:
    """用户ID输入对话框"""

    def __init__(self, parent):
        self.result = None
        self.display_name = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🔐 设置用户身份")
        self.dialog.geometry("450x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))

        # 创建界面
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="🔐 设置您的匿名身份", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))

        # 说明文字
        info_label = ttk.Label(main_frame, text="请设置您的用户ID和显示名称\n用户ID将用于其他人添加您为联系人",
                              font=("Arial", 10), foreground="gray")
        info_label.pack(pady=(0, 20))

        # 用户ID输入
        id_frame = ttk.LabelFrame(main_frame, text="用户ID", padding="10")
        id_frame.pack(fill=tk.X, pady=(0, 15))

        self.user_id_entry = ttk.Entry(id_frame, width=35, font=("Arial", 11))
        self.user_id_entry.pack(fill=tk.X)

        # 生成随机ID按钮
        def generate_random_id():
            import secrets
            random_id = f"user_{secrets.token_hex(4)}"
            self.user_id_entry.delete(0, tk.END)
            self.user_id_entry.insert(0, random_id)
            if not self.display_name_entry.get().strip():
                self.display_name_entry.delete(0, tk.END)
                self.display_name_entry.insert(0, random_id)

        generate_btn = ttk.Button(id_frame, text="生成随机ID", command=generate_random_id)
        generate_btn.pack(pady=(5, 0))

        # 显示名称输入
        name_frame = ttk.LabelFrame(main_frame, text="显示名称（可选）", padding="10")
        name_frame.pack(fill=tk.X, pady=(0, 20))

        self.display_name_entry = ttk.Entry(name_frame, width=35, font=("Arial", 11))
        self.display_name_entry.pack(fill=tk.X)

        # 预设一些示例
        examples = ["Alice", "Bob", "Charlie", "Diana", "Eve"]
        example_frame = ttk.Frame(name_frame)
        example_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(example_frame, text="快速选择:", font=("Arial", 9)).pack(side=tk.LEFT)
        for example in examples:
            def set_example(name=example):
                self.display_name_entry.delete(0, tk.END)
                self.display_name_entry.insert(0, name)
                if not self.user_id_entry.get().strip():
                    self.user_id_entry.delete(0, tk.END)
                    self.user_id_entry.insert(0, name.lower())

            btn = ttk.Button(example_frame, text=example, command=set_example, width=8)
            btn.pack(side=tk.LEFT, padx=2)

        # 初始化默认值
        generate_random_id()

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        cancel_btn = ttk.Button(button_frame, text="取消", command=self._cancel)
        cancel_btn.pack(side=tk.RIGHT, padx=(5, 0))

        ok_btn = ttk.Button(button_frame, text="确定", command=self._ok)
        ok_btn.pack(side=tk.RIGHT)

        # 绑定回车键
        self.user_id_entry.bind('<Return>', lambda e: self._ok())
        self.display_name_entry.bind('<Return>', lambda e: self._ok())
        self.dialog.bind('<Escape>', lambda e: self._cancel())

        # 焦点设置
        self.user_id_entry.focus()
        self.user_id_entry.select_range(0, tk.END)

    def _ok(self):
        user_id = self.user_id_entry.get().strip()
        if not user_id:
            messagebox.showerror("错误", "请输入用户ID")
            return

        if len(user_id) < 3:
            messagebox.showerror("错误", "用户ID至少需要3个字符")
            return

        self.result = user_id
        self.display_name = self.display_name_entry.get().strip() or user_id
        print(f"✓ 用户身份设置: ID={user_id}, 显示名称={self.display_name}")
        self.dialog.destroy()

    def _cancel(self):
        self.dialog.destroy()


class AddContactDialog:
    """添加联系人对话框"""

    def __init__(self, parent):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("添加联系人")
        self.dialog.geometry("350x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))

        # 创建界面
        ttk.Label(self.dialog, text="联系人ID:").pack(pady=5)
        self.id_entry = ttk.Entry(self.dialog, width=40)
        self.id_entry.pack(pady=5)

        ttk.Label(self.dialog, text="显示名称:").pack(pady=5)
        self.name_entry = ttk.Entry(self.dialog, width=40)
        self.name_entry.pack(pady=5)

        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="添加", command=self._ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._cancel).pack(side=tk.LEFT, padx=5)

        self.id_entry.focus()

    def _ok(self):
        contact_id = self.id_entry.get().strip()
        contact_name = self.name_entry.get().strip()

        if not contact_id:
            messagebox.showwarning("输入错误", "联系人ID不能为空")
            return

        if not contact_name:
            contact_name = contact_id

        self.result = (contact_id, contact_name)
        self.dialog.destroy()

    def _cancel(self):
        self.dialog.destroy()


class KeyManagementDialog:
    """密钥管理对话框"""

    def __init__(self, parent, key_manager):
        self.key_manager = key_manager

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("密钥管理")
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 创建界面
        ttk.Label(self.dialog, text="密钥管理", font=("Arial", 14, "bold")).pack(pady=10)

        # 密钥列表
        self.key_tree = ttk.Treeview(self.dialog, columns=("type", "algorithm", "created"), show="tree headings")
        self.key_tree.heading("#0", text="密钥ID")
        self.key_tree.heading("type", text="类型")
        self.key_tree.heading("algorithm", text="算法")
        self.key_tree.heading("created", text="创建时间")

        self.key_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(pady=10)

        ttk.Button(button_frame, text="生成新密钥", command=self._generate_key).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除密钥", command=self._delete_key).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=self._close).pack(side=tk.LEFT, padx=5)

        self._load_keys()

    def _load_keys(self):
        """加载密钥列表"""
        if not self.key_manager:
            return

        try:
            all_keys = self.key_manager.list_all_keys()
            for category, keys in all_keys.items():
                for key_info in keys:
                    created_time = datetime.fromtimestamp(key_info['created_at']).strftime("%Y-%m-%d %H:%M")
                    self.key_tree.insert("", tk.END,
                                       text=key_info['key_id'],
                                       values=(category, key_info['metadata'].get('algorithm', 'Unknown'), created_time))
        except Exception as e:
            messagebox.showerror("加载密钥错误", f"加载密钥列表失败: {e}")

    def _generate_key(self):
        """生成新密钥"""
        messagebox.showinfo("生成密钥", "密钥生成功能待实现")

    def _delete_key(self):
        """删除密钥"""
        selection = self.key_tree.selection()
        if not selection:
            messagebox.showwarning("删除密钥", "请选择要删除的密钥")
            return

        messagebox.showinfo("删除密钥", "密钥删除功能待实现")

    def _close(self):
        self.dialog.destroy()


class SecuritySettingsDialog:
    """安全设置对话框"""

    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("安全设置")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 创建界面
        ttk.Label(self.dialog, text="安全设置", font=("Arial", 14, "bold")).pack(pady=10)

        # 设置选项
        settings_frame = ttk.LabelFrame(self.dialog, text="匿名化设置")
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        self.auto_rotate = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, text="自动轮换身份", variable=self.auto_rotate).pack(anchor=tk.W, padx=10, pady=5)

        self.use_dummy_traffic = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, text="启用虚假流量", variable=self.use_dummy_traffic).pack(anchor=tk.W, padx=10, pady=5)

        # 加密设置
        crypto_frame = ttk.LabelFrame(self.dialog, text="加密设置")
        crypto_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(crypto_frame, text="电路长度:").pack(anchor=tk.W, padx=10, pady=2)
        self.circuit_length = tk.IntVar(value=3)
        ttk.Scale(crypto_frame, from_=2, to=5, variable=self.circuit_length, orient=tk.HORIZONTAL).pack(fill=tk.X, padx=10, pady=2)

        # 按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="保存", command=self._save).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._cancel).pack(side=tk.LEFT, padx=5)

    def _save(self):
        """保存设置"""
        messagebox.showinfo("保存设置", "设置保存功能待实现")
        self.dialog.destroy()

    def _cancel(self):
        self.dialog.destroy()


class HelpDialog:
    """帮助对话框"""

    def __init__(self, parent, help_text):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("使用说明")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)

        # 创建文本显示区域
        text_frame = ttk.Frame(self.dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, font=("Arial", 10))
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        ttk.Button(self.dialog, text="关闭", command=self.dialog.destroy).pack(pady=10)


class AboutDialog:
    """关于对话框"""

    def __init__(self, parent, about_text):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("关于")
        self.dialog.geometry("400x350")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)

        # 创建文本显示区域
        text_frame = ttk.Frame(self.dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 10), bg=self.dialog.cget('bg'), relief=tk.FLAT)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, about_text)
        text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        ttk.Button(self.dialog, text="关闭", command=self.dialog.destroy).pack(pady=10)


# 主程序入口
if __name__ == "__main__":
    app = MainWindow()
    app.run()
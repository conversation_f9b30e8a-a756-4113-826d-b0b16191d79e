#!/usr/bin/env python3
"""
生成自签名SSL证书用于WSS连接
"""

import os
import ssl
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa
import datetime

def generate_self_signed_cert():
    """生成自签名SSL证书"""
    
    # 创建证书目录
    cert_dir = "ssl_certs"
    os.makedirs(cert_dir, exist_ok=True)
    
    # 生成私钥
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )
    
    # 创建证书主题
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Anonymous"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "Anonymous City"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Anonymous Crypto Chat"),
        x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
    ])
    
    # 创建证书
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.utcnow()
    ).not_valid_after(
        datetime.datetime.utcnow() + datetime.timedelta(days=365)
    ).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName("localhost"),
            x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
            x509.IPAddress(ipaddress.IPv6Address("::1")),
        ]),
        critical=False,
    ).sign(private_key, hashes.SHA256())
    
    # 保存私钥
    private_key_path = os.path.join(cert_dir, "server.key")
    with open(private_key_path, "wb") as f:
        f.write(private_key.serialize(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ))
    
    # 保存证书
    cert_path = os.path.join(cert_dir, "server.crt")
    with open(cert_path, "wb") as f:
        f.write(cert.serialize(serialization.Encoding.PEM))
    
    print(f"✓ SSL证书已生成:")
    print(f"  私钥: {private_key_path}")
    print(f"  证书: {cert_path}")
    
    return cert_path, private_key_path

def create_ssl_context(cert_path: str, key_path: str) -> ssl.SSLContext:
    """创建SSL上下文"""
    context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
    context.load_cert_chain(cert_path, key_path)
    
    # 设置安全选项
    context.options |= ssl.OP_NO_SSLv2
    context.options |= ssl.OP_NO_SSLv3
    context.options |= ssl.OP_NO_TLSv1
    context.options |= ssl.OP_NO_TLSv1_1
    context.options |= ssl.OP_SINGLE_DH_USE
    context.options |= ssl.OP_SINGLE_ECDH_USE
    
    # 设置密码套件
    context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
    
    return context

def create_client_ssl_context() -> ssl.SSLContext:
    """创建客户端SSL上下文"""
    context = ssl.create_default_context()
    
    # 对于自签名证书，禁用证书验证（仅用于开发）
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    
    return context

if __name__ == "__main__":
    import ipaddress
    generate_self_signed_cert()

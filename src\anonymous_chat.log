2025-06-17 20:11:50,981 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:11:50,981 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:567> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:11:50,982 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "D:\anaconda\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
2025-06-17 20:12:07,119 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,120 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:567> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,121 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,123 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() running at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:570> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,143 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,144 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-13' coro=<MainWindow._message_receiver() running at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:570> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 09:19:28,270 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 09:19:28,271 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:725> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 09:19:30,711 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 09:19:30,712 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:725> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 10:36:32,709 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 10:36:32,710 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:725> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 10:36:35,852 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 10:36:35,852 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() running at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:728> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 19:16:33,871 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-12' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:84> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>

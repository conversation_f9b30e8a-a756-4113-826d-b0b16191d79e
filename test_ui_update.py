#!/usr/bin/env python3
"""
UI更新测试工具 - 测试GUI连接状态更新
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ui_update():
    """测试UI更新功能"""
    print("=== UI更新测试 ===")
    
    try:
        from src.gui.main_window import MainWindow
        
        # 创建主窗口
        app = MainWindow()
        
        # 模拟连接过程
        def simulate_connection():
            print("模拟连接过程...")
            
            # 模拟连接中状态
            app.connection_status.config(text="正在连接...", foreground="orange")
            app.connect_btn.config(state=tk.DISABLED)
            app.root.update()
            
            # 等待一秒
            app.root.after(1000, simulate_success)
        
        def simulate_success():
            print("模拟连接成功...")
            
            # 模拟连接成功
            app.is_connected = True
            app.user_id = "test_user_123"
            
            # 更新UI
            app.connection_status.config(text="已连接", foreground="green")
            app.identity_label.config(text=app.user_id, foreground="blue")
            app.connect_btn.config(state=tk.DISABLED)
            app.disconnect_btn.config(state=tk.NORMAL)
            
            messagebox.showinfo("测试", "UI更新测试成功！\n连接状态已更新")
        
        # 添加测试按钮
        test_frame = tk.Frame(app.root)
        test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)
        
        tk.Button(test_frame, text="测试UI更新", command=simulate_connection,
                 bg="lightblue").pack(side=tk.LEFT, padx=5)
        
        def test_real_connection():
            """测试真实连接"""
            try:
                app._connect()
            except Exception as e:
                messagebox.showerror("连接测试", f"连接测试失败: {e}")
        
        tk.Button(test_frame, text="测试真实连接", command=test_real_connection,
                 bg="lightgreen").pack(side=tk.LEFT, padx=5)
        
        # 添加状态检查
        def check_status():
            status_text = f"""
当前状态:
- 连接状态: {app.is_connected}
- 用户ID: {app.user_id}
- UI状态: {app.connection_status.cget('text')}
- 按钮状态: 连接={app.connect_btn.cget('state')}, 断开={app.disconnect_btn.cget('state')}
            """
            messagebox.showinfo("状态检查", status_text.strip())
        
        tk.Button(test_frame, text="检查状态", command=check_status,
                 bg="lightyellow").pack(side=tk.LEFT, padx=5)
        
        print("✓ UI更新测试界面已启动")
        app.run()
        
        return True
        
    except Exception as e:
        print(f"❌ UI更新测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 匿名加密通讯系统 - UI更新测试工具 ===")
    
    try:
        test_ui_update()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    main()

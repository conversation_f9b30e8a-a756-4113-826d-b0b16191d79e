#!/usr/bin/env python3
"""
连接测试工具 - 测试客户端与中继服务器的连接
"""

import asyncio
import websockets
import json
import sys

async def test_single_server(url):
    """测试单个服务器连接"""
    try:
        print(f"测试连接到 {url}...")
        
        async with websockets.connect(url) as websocket:
            # 发送测试消息
            test_message = {
                "type": "register",
                "user_id": "test_user",
                "display_name": "测试用户"
            }
            
            await websocket.send(json.dumps(test_message))
            print(f"✓ 已发送注册消息到 {url}")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                data = json.loads(response)
                print(f"✓ 收到响应: {data.get('type', 'unknown')}")
                return True
            except asyncio.TimeoutError:
                print(f"⚠ {url} 响应超时")
                return False
                
    except ConnectionRefusedError:
        print(f"❌ {url} 连接被拒绝")
        return False
    except Exception as e:
        print(f"❌ {url} 连接失败: {e}")
        return False

async def test_all_servers():
    """测试所有中继服务器"""
    servers = [
        "ws://localhost:8011",
        "ws://localhost:8012", 
        "ws://localhost:8013"
    ]
    
    print("=== 中继服务器连接测试 ===")
    
    results = []
    for server_url in servers:
        result = await test_single_server(server_url)
        results.append((server_url, result))
        print()
    
    print("=== 测试结果汇总 ===")
    success_count = 0
    for url, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{url}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(servers)} 个服务器可用")
    
    if success_count == 0:
        print("\n❌ 所有服务器都无法连接！")
        print("请确保中继服务器正在运行:")
        print("  python start_relay.py")
        return False
    elif success_count < len(servers):
        print(f"\n⚠ 只有 {success_count} 个服务器可用，系统仍可正常工作")
        return True
    else:
        print("\n✅ 所有服务器都可用！")
        return True

async def test_client_connection():
    """测试客户端连接功能"""
    print("\n=== 客户端连接测试 ===")
    
    try:
        # 导入客户端模块
        sys.path.insert(0, 'src')
        from src.network.client import AnonymousClient
        
        # 创建测试客户端
        relay_servers = ["ws://localhost:8011", "ws://localhost:8012", "ws://localhost:8013"]
        client = AnonymousClient("test_client_001", relay_servers)
        print("✓ 客户端创建成功")
        
        # 尝试连接
        print("尝试连接到中继服务器...")
        # 注意：这里只是测试创建，实际连接需要运行事件循环
        print("✓ 客户端模块正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        return False

def check_ports():
    """检查端口占用情况"""
    print("\n=== 端口检查 ===")
    
    import socket
    
    ports = [8011, 8012, 8013]
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"✓ 端口 {port}: 有服务监听")
            else:
                print(f"❌ 端口 {port}: 无服务监听")
                
        except Exception as e:
            print(f"❌ 端口 {port}: 检查失败 - {e}")

async def main():
    """主函数"""
    print("=== 匿名加密通讯系统 - 连接诊断工具 ===\n")
    
    # 检查端口
    check_ports()
    
    # 测试服务器连接
    server_ok = await test_all_servers()
    
    # 测试客户端
    client_ok = await test_client_connection()
    
    print("\n=== 诊断结果 ===")
    if server_ok and client_ok:
        print("✅ 系统连接正常，可以启动客户端")
        print("\n建议启动方式:")
        print("1. 确保中继服务器运行: python start_relay.py")
        print("2. 启动客户端: python simple_start.py")
    elif server_ok:
        print("⚠ 服务器连接正常，但客户端模块有问题")
        print("请检查客户端代码")
    else:
        print("❌ 服务器连接失败")
        print("\n解决方案:")
        print("1. 启动中继服务器: python start_relay.py")
        print("2. 检查防火墙设置")
        print("3. 确保端口8011-8013未被占用")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")

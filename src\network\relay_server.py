"""
中继服务器
实现匿名消息转发，不能解密内容或识别通信双方
"""

import asyncio
import json
import time
import secrets
from typing import Dict, Set, Optional, Any
import websockets
import logging
from dataclasses import dataclass
from .anonymizer import OnionRouter, TrafficObfuscator


@dataclass
class ClientConnection:
    """客户端连接信息"""
    websocket: websockets.WebSocketServerProtocol
    client_id: str
    connected_at: int
    last_activity: int
    bytes_transferred: int = 0
    messages_relayed: int = 0


class RelayServer:
    """匿名中继服务器"""
    
    def __init__(self, host: str = "localhost", port: int = 8001, server_id: str = None):
        self.host = host
        self.port = port
        self.server_id = server_id or f"relay_{secrets.token_hex(4)}"
        
        # 连接管理
        self.clients: Dict[str, ClientConnection] = {}
        self.websocket_to_client: Dict[websockets.WebSocketServerProtocol, str] = {}

        # 用户注册管理
        self.registered_users: Dict[str, Dict] = {}  # {user_id: {'client_id': str, 'display_name': str, 'websocket': ws, 'timestamp': int}}
        self.client_to_user: Dict[str, str] = {}  # {client_id: user_id}
        
        # 匿名化组件
        self.onion_router = OnionRouter()
        self.obfuscator = TrafficObfuscator()
        
        # 统计信息
        self.stats = {
            'start_time': int(time.time()),
            'total_connections': 0,
            'active_connections': 0,
            'messages_relayed': 0,
            'bytes_transferred': 0,
            'circuits_established': 0
        }
        
        # 配置
        self.max_connections = 1000
        self.max_message_size = 1024 * 1024  # 1MB
        self.connection_timeout = 300  # 5分钟
        
        # 日志配置
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(f"RelayServer-{self.server_id}")
    
    async def start(self):
        """启动中继服务器"""
        self.logger.info(f"启动中继服务器 {self.server_id} on {self.host}:{self.port}")
        
        # 启动WebSocket服务器
        server = await websockets.serve(
            self.handle_client,
            self.host,
            self.port,
            max_size=self.max_message_size,
            ping_interval=30,
            ping_timeout=10
        )
        
        # 启动后台任务
        tasks = [
            asyncio.create_task(self._cleanup_loop()),
            asyncio.create_task(self._stats_loop()),
            asyncio.create_task(self._generate_dummy_traffic())
        ]
        
        self.logger.info(f"✓ 中继服务器已启动: ws://{self.host}:{self.port}")
        
        try:
            await asyncio.gather(server.wait_closed(), *tasks)
        except KeyboardInterrupt:
            self.logger.info("收到停止信号，正在关闭服务器...")
        finally:
            server.close()
            await server.wait_closed()
            await self.stop()
    
    async def stop(self):
        """停止中继服务器"""
        self.logger.info("正在停止中继服务器...")
        
        # 关闭所有客户端连接
        for client_id, client in list(self.clients.items()):
            await self._disconnect_client(client_id, "Server shutdown")
        
        self.logger.info("✓ 中继服务器已停止")
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_id = None
        try:
            # 检查连接数限制
            if len(self.clients) >= self.max_connections:
                await websocket.close(code=1013, reason="Server overloaded")
                return
            
            # 生成客户端ID
            client_id = f"client_{secrets.token_hex(8)}"
            
            # 创建客户端连接记录
            client = ClientConnection(
                websocket=websocket,
                client_id=client_id,
                connected_at=int(time.time()),
                last_activity=int(time.time())
            )
            
            # 注册客户端
            self.clients[client_id] = client
            self.websocket_to_client[websocket] = client_id
            self.stats['total_connections'] += 1
            self.stats['active_connections'] += 1
            
            self.logger.info(f"✓ 客户端连接: {client_id} from {websocket.remote_address}")
            
            # 处理客户端消息
            async for message in websocket:
                await self._handle_client_message(client_id, message)
                
        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"客户端断开连接: {client_id}")
        except Exception as e:
            self.logger.error(f"处理客户端连接时出错: {e}")
        finally:
            if client_id:
                await self._disconnect_client(client_id)
    
    async def _handle_client_message(self, client_id: str, message):
        """处理客户端消息"""
        try:
            client = self.clients[client_id]
            client.last_activity = int(time.time())
            
            if isinstance(message, str):
                # 文本消息（控制消息）
                await self._handle_control_message(client_id, message)
            elif isinstance(message, bytes):
                # 二进制消息（洋葱数据包）
                await self._handle_onion_packet(client_id, message)
            
            # 更新统计
            client.messages_relayed += 1
            client.bytes_transferred += len(message) if isinstance(message, bytes) else len(message.encode())
            self.stats['messages_relayed'] += 1
            self.stats['bytes_transferred'] += len(message) if isinstance(message, bytes) else len(message.encode())
            
        except Exception as e:
            self.logger.error(f"处理客户端消息时出错: {e}")
            await self._send_error_to_client(client_id, "MESSAGE_PROCESSING_ERROR", str(e))
    
    async def _handle_control_message(self, client_id: str, message: str):
        """处理控制消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'ping':
                # 响应ping
                await self._send_to_client(client_id, json.dumps({'type': 'pong', 'timestamp': int(time.time())}))
            
            elif message_type == 'establish_circuit':
                # 建立电路请求
                circuit_id = data.get('circuit_id')
                await self._handle_circuit_establishment(client_id, circuit_id, data)
            
            elif message_type == 'relay_message':
                # 中继消息请求
                await self._handle_relay_request(client_id, data)

            elif message_type == 'register':
                # 用户注册
                await self._handle_user_registration(client_id, data)

            elif message_type == 'user_lookup':
                # 用户状态查询
                await self._handle_user_lookup(client_id, data)

            elif message_type == 'encrypted_message':
                # 加密消息转发
                await self._handle_encrypted_message(client_id, data)

            elif message_type == 'encrypted_file':
                # 加密文件转发
                await self._handle_encrypted_file(client_id, data)

            else:
                self.logger.warning(f"未知控制消息类型: {message_type}")
                
        except json.JSONDecodeError:
            self.logger.error(f"无效的JSON消息来自客户端 {client_id}")
        except Exception as e:
            self.logger.error(f"处理控制消息时出错: {e}")
    
    async def _handle_onion_packet(self, client_id: str, packet: bytes):
        """处理洋葱数据包"""
        try:
            # 检查是否为虚假流量
            if self.obfuscator.is_dummy_traffic(packet):
                # 丢弃虚假流量，不做任何处理
                return
            
            # 去除填充
            unpadded_packet = self.obfuscator.unpad_packet(packet)
            
            # 这里应该实现洋葱路由的剥离逻辑
            # 简化处理：假设这是一个需要转发的数据包
            
            # 解析路由信息（这里需要根据实际的洋葱路由协议实现）
            routing_info = await self._extract_routing_info(unpadded_packet)
            
            if routing_info:
                next_hop = routing_info.get('next_hop')
                destination = routing_info.get('destination')
                payload = routing_info.get('payload')
                
                if next_hop:
                    # 转发到下一跳
                    await self._forward_to_next_hop(next_hop, payload)
                elif destination:
                    # 到达最终目的地
                    await self._deliver_to_destination(destination, payload)
            
        except Exception as e:
            self.logger.error(f"处理洋葱数据包时出错: {e}")
    
    async def _extract_routing_info(self, packet: bytes) -> Optional[Dict[str, Any]]:
        """提取路由信息（简化实现）"""
        try:
            # 这里应该实现真正的洋葱路由解包
            # 简化处理：假设数据包格式为 [路由头长度(4字节)][路由头][载荷]
            
            if len(packet) < 4:
                return None
            
            header_length = int.from_bytes(packet[:4], 'big')
            if len(packet) < 4 + header_length:
                return None
            
            header_bytes = packet[4:4 + header_length]
            payload = packet[4 + header_length:]
            
            routing_header = json.loads(header_bytes.decode('utf-8'))
            routing_header['payload'] = payload
            
            return routing_header
            
        except Exception:
            return None
    
    async def _forward_to_next_hop(self, next_hop: str, payload: bytes):
        """转发到下一跳"""
        try:
            # 查找下一跳的连接
            target_client = None
            for client in self.clients.values():
                if client.client_id == next_hop:
                    target_client = client
                    break
            
            if target_client:
                await target_client.websocket.send(payload)
                self.logger.debug(f"消息已转发到 {next_hop}")
            else:
                self.logger.warning(f"找不到下一跳: {next_hop}")
                
        except Exception as e:
            self.logger.error(f"转发消息时出错: {e}")
    
    async def _deliver_to_destination(self, destination: str, payload: bytes):
        """投递到最终目的地"""
        try:
            # 查找目标客户端
            target_client = None
            for client in self.clients.values():
                if client.client_id == destination:
                    target_client = client
                    break
            
            if target_client:
                await target_client.websocket.send(payload)
                self.logger.debug(f"消息已投递到 {destination}")
            else:
                self.logger.warning(f"找不到目标客户端: {destination}")
                
        except Exception as e:
            self.logger.error(f"投递消息时出错: {e}")
    
    async def _handle_circuit_establishment(self, client_id: str, circuit_id: str, data: Dict):
        """处理电路建立"""
        try:
            # 记录电路信息
            self.stats['circuits_established'] += 1
            
            # 响应电路建立成功
            response = {
                'type': 'circuit_established',
                'circuit_id': circuit_id,
                'status': 'success'
            }
            await self._send_to_client(client_id, json.dumps(response))
            
            self.logger.info(f"电路已建立: {circuit_id} for client {client_id}")
            
        except Exception as e:
            self.logger.error(f"建立电路时出错: {e}")
    
    async def _handle_relay_request(self, client_id: str, data: Dict):
        """处理中继请求"""
        try:
            target = data.get('target')
            message = data.get('message')
            
            if target and message:
                # 查找目标客户端
                target_client = None
                for client in self.clients.values():
                    if client.client_id == target:
                        target_client = client
                        break
                
                if target_client:
                    await target_client.websocket.send(message)
                    self.logger.debug(f"消息已中继: {client_id} -> {target}")
                else:
                    await self._send_error_to_client(client_id, "TARGET_NOT_FOUND", f"Target {target} not found")
            
        except Exception as e:
            self.logger.error(f"处理中继请求时出错: {e}")

    async def _handle_user_registration(self, client_id: str, data: Dict):
        """处理用户注册"""
        try:
            user_id = data.get('user_id')
            display_name = data.get('display_name', user_id)

            if not user_id:
                await self._send_error_to_client(client_id, "INVALID_USER_ID", "User ID is required")
                return

            # 检查用户是否已注册
            if user_id in self.registered_users:
                # 检查是否是同一个客户端的重复注册
                old_client_id = self.registered_users[user_id]['client_id']
                if old_client_id == client_id:
                    # 同一个客户端的重复注册，直接返回成功
                    response = {
                        'type': 'register_success',
                        'user_id': user_id,
                        'display_name': display_name,
                        'timestamp': int(time.time()),
                        'note': 'already_registered'
                    }
                    await self._send_to_client(client_id, json.dumps(response))
                    self.logger.info(f"✓ 用户重复注册: {user_id} -> {client_id}")
                    return
                else:
                    # 不同客户端注册相同用户ID，这可能是问题
                    self.logger.warning(f"⚠️ 用户ID冲突: {user_id} 已被 {old_client_id} 注册，现在 {client_id} 也要注册")
                    # 允许新的注册，但记录警告
                    if old_client_id in self.client_to_user:
                        del self.client_to_user[old_client_id]

            # 注册用户
            client = self.clients[client_id]
            self.registered_users[user_id] = {
                'client_id': client_id,
                'display_name': display_name,
                'websocket': client.websocket,
                'timestamp': int(time.time())
            }
            self.client_to_user[client_id] = user_id

            # 发送注册成功响应
            response = {
                'type': 'register_success',
                'user_id': user_id,
                'display_name': display_name,
                'timestamp': int(time.time())
            }
            await self._send_to_client(client_id, json.dumps(response))

            self.logger.info(f"✓ 用户注册成功: {user_id} ({display_name}) -> {client_id}")

        except Exception as e:
            self.logger.error(f"处理用户注册时出错: {e}")
            await self._send_error_to_client(client_id, "REGISTRATION_ERROR", str(e))

    async def _handle_user_lookup(self, client_id: str, data: Dict):
        """处理用户状态查询"""
        try:
            target_user_id = data.get('target_user_id')
            query_id = data.get('query_id')  # 获取查询ID

            if not target_user_id:
                await self._send_error_to_client(client_id, "INVALID_TARGET", "Target user ID is required")
                return

            # 检查目标用户是否在线
            is_online = target_user_id in self.registered_users

            response = {
                'type': 'user_lookup_response',  # 保持一致的消息类型
                'target_user_id': target_user_id,
                'is_online': is_online,
                'query_id': query_id,  # 返回查询ID
                'timestamp': int(time.time())
            }

            if is_online:
                user_info = self.registered_users[target_user_id]
                response['display_name'] = user_info['display_name']
                response['last_seen'] = user_info['timestamp']

            await self._send_to_client(client_id, json.dumps(response))

            self.logger.info(f"✓ 用户查询: {target_user_id} -> {'在线' if is_online else '离线'} (查询ID: {query_id})")

        except Exception as e:
            self.logger.error(f"处理用户查询时出错: {e}")
            await self._send_error_to_client(client_id, "LOOKUP_ERROR", str(e))

    async def _handle_encrypted_message(self, client_id: str, data: Dict):
        """处理加密消息转发"""
        try:
            target_user_id = data.get('target_user_id')
            encrypted_content = data.get('encrypted_content')

            if not target_user_id or not encrypted_content:
                await self._send_error_to_client(client_id, "INVALID_MESSAGE", "Target user ID and content are required")
                return

            # 查找目标用户
            if target_user_id not in self.registered_users:
                await self._send_error_to_client(client_id, "USER_NOT_FOUND", f"User {target_user_id} not found")
                return

            # 获取发送者信息
            sender_user_id = self.client_to_user.get(client_id, "unknown")

            # 转发消息到目标用户
            target_info = self.registered_users[target_user_id]
            target_client_id = target_info['client_id']

            message_data = {
                'type': 'encrypted_message_received',
                'sender_user_id': sender_user_id,
                'encrypted_content': encrypted_content,
                'timestamp': data.get('timestamp', int(time.time()))
            }

            await self._send_to_client(target_client_id, json.dumps(message_data))

            # 发送确认给发送者
            confirmation = {
                'type': 'message_delivered',
                'target_user_id': target_user_id,
                'timestamp': int(time.time())
            }
            await self._send_to_client(client_id, json.dumps(confirmation))

            self.logger.info(f"✓ 消息转发: {sender_user_id} -> {target_user_id}")

        except Exception as e:
            self.logger.error(f"处理加密消息时出错: {e}")
            await self._send_error_to_client(client_id, "MESSAGE_RELAY_ERROR", str(e))

    async def _handle_encrypted_file(self, client_id: str, data: Dict):
        """处理加密文件转发"""
        try:
            target_user_id = data.get('target_user_id')
            filename = data.get('filename')
            file_data = data.get('file_data')

            if not target_user_id or not filename or not file_data:
                await self._send_error_to_client(client_id, "INVALID_FILE", "Target user ID, filename and file data are required")
                return

            # 查找目标用户
            if target_user_id not in self.registered_users:
                await self._send_error_to_client(client_id, "USER_NOT_FOUND", f"User {target_user_id} not found")
                return

            # 获取发送者信息
            sender_user_id = self.client_to_user.get(client_id, "unknown")

            # 转发文件到目标用户
            target_info = self.registered_users[target_user_id]
            target_client_id = target_info['client_id']

            file_message = {
                'type': 'encrypted_file_received',
                'sender_user_id': sender_user_id,
                'filename': filename,
                'file_data': file_data,
                'file_size': data.get('file_size', len(file_data)),
                'timestamp': data.get('timestamp', int(time.time()))
            }

            await self._send_to_client(target_client_id, json.dumps(file_message))

            # 发送确认给发送者
            confirmation = {
                'type': 'file_delivered',
                'target_user_id': target_user_id,
                'filename': filename,
                'timestamp': int(time.time())
            }
            await self._send_to_client(client_id, json.dumps(confirmation))

            self.logger.info(f"✓ 文件转发: {sender_user_id} -> {target_user_id} ({filename})")

        except Exception as e:
            self.logger.error(f"处理加密文件时出错: {e}")
            await self._send_error_to_client(client_id, "FILE_RELAY_ERROR", str(e))

    async def _send_to_client(self, client_id: str, message: str):
        """发送消息到客户端"""
        try:
            if client_id in self.clients:
                client = self.clients[client_id]
                await client.websocket.send(message)
        except Exception as e:
            self.logger.error(f"发送消息到客户端 {client_id} 时出错: {e}")
    
    async def _send_error_to_client(self, client_id: str, error_code: str, error_message: str):
        """发送错误消息到客户端"""
        error_data = {
            'type': 'error',
            'error_code': error_code,
            'error_message': error_message,
            'timestamp': int(time.time())
        }
        await self._send_to_client(client_id, json.dumps(error_data))

    async def _disconnect_client(self, client_id: str, reason: str = "Unknown"):
        """断开客户端连接"""
        try:
            if client_id in self.clients:
                client = self.clients[client_id]

                # 从映射中移除
                if client.websocket in self.websocket_to_client:
                    del self.websocket_to_client[client.websocket]

                # 移除用户注册
                if client_id in self.client_to_user:
                    user_id = self.client_to_user[client_id]
                    if user_id in self.registered_users:
                        del self.registered_users[user_id]
                        self.logger.info(f"用户已注销: {user_id}")
                    del self.client_to_user[client_id]

                # 关闭WebSocket连接
                if not client.websocket.closed:
                    await client.websocket.close(reason=reason)

                # 移除客户端记录
                del self.clients[client_id]
                self.stats['active_connections'] -= 1

                self.logger.info(f"客户端已断开: {client_id} - {reason}")

        except Exception as e:
            self.logger.error(f"断开客户端连接时出错: {e}")

    # 后台任务
    async def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                current_time = int(time.time())
                expired_clients = []

                # 查找过期连接
                for client_id, client in self.clients.items():
                    if current_time - client.last_activity > self.connection_timeout:
                        expired_clients.append(client_id)

                # 断开过期连接
                for client_id in expired_clients:
                    await self._disconnect_client(client_id, "Connection timeout")

                if expired_clients:
                    self.logger.info(f"清理了 {len(expired_clients)} 个过期连接")

                await asyncio.sleep(60)  # 每分钟清理一次

            except Exception as e:
                self.logger.error(f"清理循环出错: {e}")
                await asyncio.sleep(10)

    async def _stats_loop(self):
        """统计循环"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟输出一次统计

                uptime = int(time.time()) - self.stats['start_time']
                self.logger.info(
                    f"统计信息 - 运行时间: {uptime}s, "
                    f"活跃连接: {self.stats['active_connections']}, "
                    f"总连接: {self.stats['total_connections']}, "
                    f"中继消息: {self.stats['messages_relayed']}, "
                    f"传输字节: {self.stats['bytes_transferred']}"
                )

            except Exception as e:
                self.logger.error(f"统计循环出错: {e}")

    async def _generate_dummy_traffic(self):
        """生成虚假流量"""
        while True:
            try:
                await asyncio.sleep(secrets.randbelow(30) + 10)  # 10-40秒随机间隔

                if len(self.clients) >= 2:
                    # 随机选择两个客户端
                    client_ids = list(self.clients.keys())
                    sender = secrets.choice(client_ids)
                    receiver = secrets.choice([c for c in client_ids if c != sender])

                    # 生成虚假流量
                    dummy_packet = self.obfuscator.generate_dummy_traffic()

                    # 发送虚假流量
                    sender_client = self.clients[sender]
                    await sender_client.websocket.send(dummy_packet)

            except Exception as e:
                self.logger.error(f"生成虚假流量时出错: {e}")


# 示例使用
if __name__ == "__main__":
    async def main():
        # 创建中继服务器
        server = RelayServer("localhost", 8001, "relay_001")

        try:
            await server.start()
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
        finally:
            await server.stop()

    asyncio.run(main())

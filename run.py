#!/usr/bin/env python3
import os
import sys
import subprocess

def install_requirements():
    """安装依赖"""
    try:
        subprocess.check_call([
            sys.executable,
            "-m", "pip", "install",
            "-r", "requirements.txt"
        ])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 检查是否存在虚拟环境
    if not os.path.exists("venv"):
        print("创建虚拟环境...")
        try:
            subprocess.check_call([
                sys.executable,
                "-m", "venv",
                "venv"
            ])
        except subprocess.CalledProcessError:
            print("错误: 创建虚拟环境失败")
            sys.exit(1)
    
    # 激活虚拟环境
    if sys.platform == "win32":
        python = os.path.join("venv", "<PERSON>rip<PERSON>", "python.exe")
    else:
        python = os.path.join("venv", "bin", "python")
    
    # 安装依赖
    print("安装依赖...")
    if not install_requirements():
        print("错误: 安装依赖失败")
        sys.exit(1)
    
    # 运行程序
    print("启动程序...")
    try:
        # 将当前目录添加到PYTHONPATH
        env = os.environ.copy()
        env["PYTHONPATH"] = os.path.dirname(os.path.abspath(__file__))
        
        subprocess.check_call([
            python,
            "-m", "src.main"
        ], env=env)
    except subprocess.CalledProcessError:
        print("错误: 程序运行失败")
        sys.exit(1)

if __name__ == "__main__":
    main() 
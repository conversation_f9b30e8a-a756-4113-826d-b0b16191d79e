# 匿名加密通讯系统

## 项目概述

这是一个基于Python的匿名加密通讯系统，实现了多层加密和匿名化技术，确保通信双方的身份和内容都无法被第三方获取。

## 核心特性

### 多层加密保护
- **对称加密**: AES-256-GCM 用于消息内容加密
- **非对称加密**: RSA-4096/ECC-P384 用于密钥交换
- **密钥派生**: ECDH + HKDF 用于会话密钥生成
- **消息认证**: HMAC-SHA256 确保消息完整性
- **前向安全**: 每次会话使用新的临时密钥

### 匿名化技术
- **洋葱路由**: 多层代理隐藏通信路径
- **混合网络**: 随机延迟和批处理消息
- **身份隐藏**: 临时身份标识符
- **流量混淆**: 虚假流量和填充数据

### 安全传输
- **端到端加密**: 只有通信双方能解密消息
- **传输层安全**: TLS 1.3 + 自定义加密层
- **抗重放攻击**: 时间戳和序列号验证
- **抗流量分析**: 固定大小数据包和随机延迟

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端 A      │    │   中继节点      │    │   客户端 B      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 加密模块    │ │    │ │ 路由转发    │ │    │ │ 加密模块    │ │
│ │ - AES-256   │ │    │ │ - 盲转发    │ │    │ │ - AES-256   │ │
│ │ - RSA/ECC   │ │    │ │ - 流量混淆  │ │    │ │ - RSA/ECC   │ │
│ │ - ECDH      │ │    │ │ - 延迟随机  │ │    │ │ - ECDH      │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 匿名化层    │ │◄──►│ │ 匿名化层    │ │◄──►│ │ 匿名化层    │ │
│ │ - 洋葱路由  │ │    │ │ - 身份隐藏  │ │    │ │ - 洋葱路由  │ │
│ │ - 代理链    │ │    │ │ - 路径隐藏  │ │    │ │ - 代理链    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 用户界面    │ │    │ │ 管理界面    │ │    │ │ 用户界面    │ │
│ │ - 消息聊天  │ │    │ │ - 节点状态  │ │    │ │ - 消息聊天  │ │
│ │ - 文件传输  │ │    │ │ - 流量统计  │ │    │ │ - 文件传输  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 目录结构

```
anonymous_crypto_chat/
├── src/
│   ├── crypto/              # 加密模块
│   │   ├── symmetric.py     # 对称加密 (AES)
│   │   ├── asymmetric.py    # 非对称加密 (RSA/ECC)
│   │   ├── key_exchange.py  # 密钥交换 (ECDH)
│   │   ├── hashing.py       # 哈希和MAC
│   │   └── key_manager.py   # 密钥管理
│   ├── network/             # 网络通信模块
│   │   ├── protocol.py      # 通信协议
│   │   ├── relay_server.py  # 中继服务器
│   │   ├── client.py        # 客户端网络层
│   │   └── anonymizer.py    # 匿名化层
│   ├── gui/                 # 图形界面
│   │   ├── main_window.py   # 主窗口
│   │   ├── chat_widget.py   # 聊天界面
│   │   └── file_transfer.py # 文件传输界面
│   ├── utils/               # 工具模块
│   │   ├── config.py        # 配置管理
│   │   ├── logger.py        # 日志系统
│   │   └── helpers.py       # 辅助函数
│   └── main.py              # 主程序入口
├── tests/                   # 测试文件
├── docs/                    # 文档
├── config/                  # 配置文件
└── requirements.txt         # 依赖包
```

## 安装和运行

1. 安装依赖:
```bash
pip install -r requirements.txt
```

2. 运行客户端:
```bash
python src/main.py --mode client
```

3. 运行中继服务器:
```bash
python src/main.py --mode relay
```

## 安全特性

- **零知识架构**: 中继服务器无法获取任何明文信息
- **完美前向保密**: 即使长期密钥泄露，历史消息仍然安全
- **抗量子攻击**: 支持后量子密码学算法
- **流量分析防护**: 通过填充和混淆防止流量分析
- **元数据保护**: 隐藏通信时间、频率和模式

## 技术栈

- **Python 3.9+**: 主要开发语言
- **cryptography**: 核心加密库
- **asyncio**: 异步网络编程
- **websockets**: WebSocket通信
- **tkinter/customtkinter**: GUI界面
- **pysocks**: SOCKS代理支持
- **stem**: Tor网络集成



用户A输入消息 "Hello"
    ↓
[1. 生成随机AES密钥] → [2. AES加密消息]
    ↓
[3. RSA加密AES密钥] → [4. 组合E2E数据包]
    ↓
[5. 选择洋葱路径: R1→R2→R3→用户B]
    ↓
[6. 第3层加密(R3密钥)] → [7. 第2层加密(R2密钥)]
    ↓
[8. 第1层加密(R1密钥)] → [9. 发送到R1]
    ↓
[R1解密第1层] → [转发到R2]
    ↓
[R2解密第2层] → [转发到R3]
    ↓
[R3解密第3层] → [转发E2E数据包到用户B]
    ↓
[用户B RSA解密AES密钥] → [AES解密消息]
    ↓
用户B看到 "Hello"
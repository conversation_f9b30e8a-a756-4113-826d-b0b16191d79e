#!/usr/bin/env python3
"""
简化的启动脚本 - 用于测试和恢复项目状态
"""

import sys
import os
import asyncio

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试所有模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        print("测试基础模块...")
        import tkinter as tk
        print("✓ tkinter 可用")
        
        import websockets
        print("✓ websockets 可用")
        
        import cryptography
        print("✓ cryptography 可用")
        
    except ImportError as e:
        print(f"❌ 基础模块导入失败: {e}")
        return False
    
    try:
        print("\n测试项目模块...")
        
        # 测试加密模块
        from src.crypto.symmetric import SymmetricCrypto
        print("✓ crypto.symmetric 导入成功")

        # 测试网络模块
        from src.network.client import AnonymousClient
        print("✓ network.client 导入成功")

        # 测试GUI模块
        from src.gui.main_window import MainWindow
        print("✓ gui.main_window 导入成功")
        
        print("\n✅ 所有模块导入成功！")
        return True
        
    except ImportError as e:
        print(f"❌ 项目模块导入失败: {e}")
        return False

def test_crypto():
    """测试加密功能"""
    print("\n=== 测试加密功能 ===")
    
    try:
        from src.crypto.symmetric import SymmetricCrypto
        
        crypto = SymmetricCrypto()
        key = crypto.generate_key()
        message = "测试消息"
        
        encrypted = crypto.encrypt_message(message, key)
        decrypted = crypto.decrypt_message(encrypted, key)
        
        if message == decrypted:
            print("✅ 加密功能正常")
            return True
        else:
            print("❌ 加密功能异常")
            return False
            
    except Exception as e:
        print(f"❌ 加密测试失败: {e}")
        return False

def start_simple_relay():
    """启动简化的中继服务器"""
    print("\n=== 启动简化中继服务器 ===")
    
    try:
        import asyncio
        import websockets
        import json
        
        async def handle_client(websocket, path):
            print(f"客户端连接: {websocket.remote_address}")
            try:
                async for message in websocket:
                    data = json.loads(message)
                    print(f"收到消息: {data}")
                    
                    # 简单回显
                    response = {"type": "echo", "data": data}
                    await websocket.send(json.dumps(response))
                    
            except websockets.exceptions.ConnectionClosed:
                print("客户端断开连接")
        
        async def main():
            server = await websockets.serve(handle_client, "localhost", 8001)
            print("✅ 简化中继服务器启动在 ws://localhost:8001")
            await server.wait_closed()
        
        asyncio.run(main())
        
    except Exception as e:
        print(f"❌ 中继服务器启动失败: {e}")

def start_simple_client():
    """启动简化的客户端"""
    print("\n=== 启动简化客户端 ===")
    
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.title("匿名加密通讯系统 - 简化版")
        root.geometry("600x400")
        
        # 创建简单界面
        frame = tk.Frame(root)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        tk.Label(frame, text="匿名加密通讯系统", font=("Arial", 16, "bold")).pack(pady=10)
        
        status_label = tk.Label(frame, text="状态: 就绪", fg="green")
        status_label.pack(pady=5)
        
        def test_connection():
            try:
                import asyncio
                import websockets
                import json
                
                async def test():
                    try:
                        async with websockets.connect("ws://localhost:8001") as ws:
                            test_msg = {"type": "test", "message": "hello"}
                            await ws.send(json.dumps(test_msg))
                            response = await ws.recv()
                            print(f"服务器响应: {response}")
                            status_label.config(text="状态: 连接成功", fg="green")
                            messagebox.showinfo("连接测试", "连接成功！")
                    except Exception as e:
                        status_label.config(text=f"状态: 连接失败 - {e}", fg="red")
                        messagebox.showerror("连接测试", f"连接失败: {e}")
                
                asyncio.run(test())
                
            except Exception as e:
                messagebox.showerror("错误", f"测试失败: {e}")
        
        tk.Button(frame, text="测试连接", command=test_connection).pack(pady=10)
        
        def start_full_client():
            try:
                from src.gui.main_window import MainWindow
                root.destroy()
                app = MainWindow()
                app.run()
            except Exception as e:
                messagebox.showerror("启动错误", f"无法启动完整客户端: {e}")
        
        tk.Button(frame, text="启动完整客户端", command=start_full_client).pack(pady=10)
        
        tk.Label(frame, text="项目状态: 已恢复", fg="blue").pack(pady=20)
        
        print("✅ 简化客户端界面已启动")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 客户端启动失败: {e}")

def main():
    """主函数"""
    print("=== 匿名加密通讯系统 - 项目恢复工具 ===")
    
    # 测试导入
    if not test_imports():
        print("\n❌ 模块导入测试失败，请检查项目结构")
        return 1
    
    # 测试加密
    if not test_crypto():
        print("\n❌ 加密功能测试失败")
        return 1
    
    print("\n✅ 项目基础功能正常！")
    
    # 询问用户要启动什么
    print("\n请选择要启动的组件:")
    print("1. 简化客户端 (推荐)")
    print("2. 简化中继服务器")
    print("3. 完整系统测试")
    print("4. 退出")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            start_simple_client()
        elif choice == "2":
            start_simple_relay()
        elif choice == "3":
            print("运行完整系统测试...")
            # 这里可以添加更多测试
            print("✅ 系统测试完成")
        elif choice == "4":
            print("退出")
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 运行错误: {e}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

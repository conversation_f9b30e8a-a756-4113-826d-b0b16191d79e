#!/usr/bin/env python3
"""
启动客户端
"""

import os
import sys

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
sys.path.insert(0, src_dir)

# 导入并运行主程序
from main import run_client

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="匿名加密通讯客户端")
    parser.add_argument("--instance-id", help="客户端实例ID")
    args = parser.parse_args()
    
    sys.exit(run_client(args.instance_id)) 
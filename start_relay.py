#!/usr/bin/env python3
"""
启动中继服务器
"""

import asyncio
import websockets
import json
import logging
import time
from typing import Dict, Set

# 设置日志级别
logging.basicConfig(level=logging.INFO)

class SimpleRelayServer:
    def __init__(self, server_id: str, host: str, port: int):
        self.server_id = server_id
        self.host = host
        self.port = port
        self.clients = {}  # client_id -> websocket
        self.client_to_user = {}  # client_id -> user_id
        self.anonymous_users = {}  # user_id -> client_id
        
        # 设置日志
        self.logger = logging.getLogger(f"RelayServer-{server_id}")
        self.logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    async def start(self):
        """启动服务器"""
        self.logger.info(f"启动中继服务器 {self.server_id} on {self.host}:{self.port}")
        
        # 修复WebSocket处理器函数签名
        async def handle_client(websocket):
            await self._handle_client_connection(websocket, None)
        
        server = await websockets.serve(handle_client, self.host, self.port)
        self.logger.info(f"✓ 中继服务器已启动: ws://{self.host}:{self.port}")
        
        # 定期统计
        asyncio.create_task(self._periodic_stats())
        
        await server.wait_closed()

    async def _handle_client_connection(self, websocket, path=None):
        """处理客户端连接"""
        client_id = f"client_{id(websocket):016x}"
        self.clients[client_id] = websocket
        
        try:
            self.logger.info(f"✓ 客户端连接: {client_id} from {websocket.remote_address}")
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self._handle_message(client_id, data)
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON解析错误: {e}")
                    await self._send_error(client_id, "INVALID_JSON", "Invalid JSON format")
                except Exception as e:
                    self.logger.error(f"处理消息时出错: {e}")
                    await self._send_error(client_id, "MESSAGE_ERROR", str(e))
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"客户端正常断开: {client_id}")
        except Exception as e:
            self.logger.error(f"客户端连接错误: {e}")
        finally:
            await self._cleanup_client(client_id)

    async def _handle_message(self, client_id: str, data: Dict):
        """处理客户端消息"""
        try:
            message_type = data.get('type')
            self.logger.debug(f"处理消息类型: {message_type} from {client_id}")
            
            if message_type == 'register':
                await self._handle_register(client_id, data)
            elif message_type == 'anonymous_message':
                await self._handle_anonymous_message(client_id, data)
            elif message_type == 'encrypted_message':
                await self._handle_encrypted_message(client_id, data)
            elif message_type == 'encrypted_file':
                await self._handle_encrypted_file(client_id, data)
            elif message_type == 'key_exchange_request':
                await self._handle_key_exchange(client_id, data)
            elif message_type == 'key_exchange_response':
                await self._handle_key_exchange(client_id, data)
            elif message_type == 'onion_packet':
                await self._handle_onion_packet(client_id, data)
            elif message_type == 'user_lookup':
                await self._handle_user_lookup(client_id, data)
            elif message_type == 'ping':
                await self._handle_ping(client_id, data)
            else:
                await self._send_error(client_id, "UNKNOWN_MESSAGE_TYPE", f"Unknown message type: {message_type}")
        except Exception as e:
            self.logger.error(f"处理消息时出错: {e}")
            await self._send_error(client_id, "MESSAGE_PROCESSING_ERROR", str(e))

    async def _handle_register(self, client_id: str, data: Dict):
        """处理用户注册"""
        user_id = data.get('user_id')
        display_name = data.get('display_name', user_id)
        
        if not user_id:
            await self._send_error(client_id, "INVALID_USER_ID", "User ID is required")
            return
        
        # 注册用户
        self.client_to_user[client_id] = user_id
        self.anonymous_users[user_id] = client_id
        
        # 发送注册成功响应
        response = {
            'type': 'register_success',
            'user_id': user_id,
            'display_name': display_name,
            'timestamp': int(time.time())
        }
        await self._send_to_client(client_id, json.dumps(response))
        self.logger.info(f"✓ 用户注册成功: {user_id} -> {client_id}")

    async def _handle_anonymous_message(self, client_id: str, data: Dict):
        """处理匿名消息"""
        await self._relay_message(client_id, data, 'anonymous_message_received')

    async def _handle_encrypted_message(self, client_id: str, data: Dict):
        """处理加密消息"""
        await self._relay_message(client_id, data, 'encrypted_message_received')

    async def _handle_encrypted_file(self, client_id: str, data: Dict):
        """处理加密文件"""
        await self._relay_message(client_id, data, 'encrypted_file_received')

    async def _handle_key_exchange(self, client_id: str, data: Dict):
        """处理密钥交换"""
        message_type = data.get('type')
        response_type = 'key_exchange_request' if message_type == 'key_exchange_request' else 'key_exchange_response'
        await self._relay_message(client_id, data, response_type)

    async def _handle_onion_packet(self, client_id: str, data: Dict):
        """处理洋葱路由包"""
        try:
            target_user_id = data.get('target_user_id')
            circuit_id = data.get('circuit_id')
            packet_data = data.get('packet_data')
            
            # 获取发送者用户ID
            sender_user_id = self.client_to_user.get(client_id)
            if not sender_user_id:
                await self._send_error(client_id, "NOT_REGISTERED", "Client not registered")
                return
            
            if not target_user_id:
                await self._send_error(client_id, "INVALID_TARGET", "Target user ID is required")
                return
            
            # 查找目标客户端
            target_client_id = self.anonymous_users.get(target_user_id)
            if not target_client_id:
                await self._send_error(client_id, "TARGET_OFFLINE", f"User {target_user_id} is offline")
                return
            
            # 构造洋葱路由包消息，确保包含发送者ID
            onion_message = {
                'type': 'onion_packet_received',
                'sender_user_id': sender_user_id,  # 重要：添加发送者ID
                'circuit_id': circuit_id,
                'packet_data': packet_data,
                'hops': data.get('hops', 3),
                'timestamp': int(time.time())
            }
            
            # 发送到目标客户端
            await self._send_to_client(target_client_id, json.dumps(onion_message))
            
            # 发送确认给发送者
            confirmation = {
                'type': 'onion_packet_sent',
                'target_user_id': target_user_id,
                'circuit_id': circuit_id,
                'timestamp': int(time.time())
            }
            await self._send_to_client(client_id, json.dumps(confirmation))
            
            self.logger.info(f"✓ 洋葱路由包已转发: {sender_user_id} -> {target_user_id} (电路 {circuit_id})")
            
        except Exception as e:
            self.logger.error(f"处理洋葱路由包时出错: {e}")
            await self._send_error(client_id, "ONION_ROUTING_ERROR", str(e))

    async def _handle_user_lookup(self, client_id: str, data: Dict):
        """处理用户查找请求"""
        try:
            target_user_id = data.get('target_user_id')
            query_id = data.get('query_id')  # 获取查询ID

            if not target_user_id:
                await self._send_error(client_id, "INVALID_USER_LOOKUP", "Target user ID is required")
                return

            # 检查目标用户是否在线
            is_online = target_user_id in self.anonymous_users
            target_client_id = self.anonymous_users.get(target_user_id)

            # 发送查找结果 - 使用与客户端兼容的格式
            lookup_result = {
                'type': 'user_lookup_response',  # 改为 user_lookup_response 保持一致
                'target_user_id': target_user_id,
                'is_online': is_online,          # 改为 is_online 保持一致
                'query_id': query_id,            # 添加查询ID
                'timestamp': int(time.time())
            }

            await self._send_to_client(client_id, json.dumps(lookup_result))

            self.logger.info(f"✓ 用户查找: {target_user_id} -> {'在线' if is_online else '离线'} (查询ID: {query_id})")

        except Exception as e:
            self.logger.error(f"处理用户查找时出错: {e}")
            await self._send_error(client_id, "USER_LOOKUP_ERROR", str(e))

    async def _handle_ping(self, client_id: str, data: Dict):
        """处理ping请求"""
        try:
            # 发送pong响应
            pong_response = {
                'type': 'pong',
                'timestamp': int(time.time()),
                'server_id': self.server_id
            }
            
            await self._send_to_client(client_id, json.dumps(pong_response))
            self.logger.debug(f"✓ Ping响应发送到 {client_id}")
            
        except Exception as e:
            self.logger.error(f"处理ping时出错: {e}")
            await self._send_error(client_id, "PING_ERROR", str(e))

    async def _relay_message(self, client_id: str, data: Dict, response_type: str):
        """通用消息中继"""
        target_user_id = data.get('target_user_id')
        sender_user_id = self.client_to_user.get(client_id)
        
        if not sender_user_id:
            await self._send_error(client_id, "NOT_REGISTERED", "Client not registered")
            return
        
        if not target_user_id:
            await self._send_error(client_id, "INVALID_TARGET", "Target user ID is required")
            return
        
        target_client_id = self.anonymous_users.get(target_user_id)
        if not target_client_id:
            await self._send_error(client_id, "TARGET_OFFLINE", f"User {target_user_id} is offline")
            return
        
        # 构造转发消息
        relay_message = {
            'type': response_type,
            'sender_user_id': sender_user_id,
            'timestamp': int(time.time())
        }
        relay_message.update({k: v for k, v in data.items() if k not in ['type', 'target_user_id']})
        
        # 发送到目标客户端
        await self._send_to_client(target_client_id, json.dumps(relay_message))
        
        # 发送确认给发送者
        confirmation = {
            'type': 'message_sent',
            'target_user_id': target_user_id,
            'timestamp': int(time.time())
        }
        await self._send_to_client(client_id, json.dumps(confirmation))
        
        message_desc = data.get('filename', data.get('message', 'message'))
        self.logger.info(f"✓ 消息已中继: {sender_user_id} -> {target_user_id} ({message_desc[:20]}...)")

    async def _send_to_client(self, client_id: str, message: str):
        """发送消息到客户端"""
        websocket = self.clients.get(client_id)
        if websocket:
            try:
                await websocket.send(message)
            except Exception as e:
                self.logger.error(f"发送消息到客户端 {client_id} 时出错: {e}")

    async def _send_error(self, client_id: str, error_code: str, error_message: str):
        """发送错误消息"""
        error_response = {
            'type': 'error',
            'error_code': error_code,
            'error_message': error_message,
            'timestamp': int(time.time())
        }
        await self._send_to_client(client_id, json.dumps(error_response))

    async def _cleanup_client(self, client_id: str):
        """清理客户端连接"""
        user_id = self.client_to_user.get(client_id)
        if user_id:
            del self.client_to_user[client_id]
            if user_id in self.anonymous_users:
                del self.anonymous_users[user_id]
        
        if client_id in self.clients:
            del self.clients[client_id]
        
        self.logger.info(f"客户端已断开: {client_id}")

    async def _periodic_stats(self):
        """定期统计信息"""
        while True:
            await asyncio.sleep(300)  # 5分钟
            active_connections = len(self.clients)
            self.logger.info(f"统计信息 - 活跃连接: {active_connections}")

async def main():
    """启动中继网络"""
    print("启动中继网络，端口: [8011, 8012, 8013]")
    print("✓ 启动了 3 个中继服务器")
    
    servers = []
    for i, port in enumerate([8011, 8012, 8013]):  # 使用不同的端口
        server_id = f'relay_{i+1:03d}'
        server = SimpleRelayServer(server_id, 'localhost', port)
        servers.append(server)
    
    # 启动所有服务器
    tasks = [server.start() for server in servers]
    await asyncio.gather(*tasks)

if __name__ == "__main__":
    asyncio.run(main())

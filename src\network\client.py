"""
客户端网络层
处理与中继服务器的连接和匿名通信
"""

import asyncio
import json
import time
import secrets
from typing import Dict, Optional, Callable, Any, List
import websockets
import aiohttp
from .protocol import SecureProtocol, ProtocolMessage, MessageType
from .anonymizer import OnionRouter, TrafficObfuscator, AnonymousIdentityManager
from ..crypto import ECDHKeyExchange, SymmetricCrypto


class AnonymousClient:
    """匿名客户端"""
    
    def __init__(self, user_id: str, relay_servers: List[str]):
        self.user_id = user_id
        self.relay_servers = relay_servers
        self.protocol = SecureProtocol(user_id)
        self.onion_router = OnionRouter()
        self.obfuscator = TrafficObfuscator()
        self.identity_manager = AnonymousIdentityManager()
        self.key_exchange = ECDHKeyExchange()
        
        # 连接管理
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.active_circuits: Dict[str, str] = {}  # session_id -> circuit_id
        self.message_handlers: Dict[MessageType, Callable] = {}
        self.running = False
        self.is_registered = False

        # 简单消息处理器（用于中继服务器通信）
        self.relay_message_handlers: Dict[str, Callable] = {}
        
        # 初始化消息处理器
        self._setup_message_handlers()
        
        # 创建初始身份
        self.current_identity = self.identity_manager.create_identity()
        self.identity_manager.switch_identity(self.current_identity)
    
    def _setup_message_handlers(self):
        """设置消息处理器"""
        self.message_handlers = {
            MessageType.HANDSHAKE_INIT: self._handle_handshake_init,
            MessageType.HANDSHAKE_RESPONSE: self._handle_handshake_response,
            MessageType.HANDSHAKE_COMPLETE: self._handle_handshake_complete,
            MessageType.TEXT_MESSAGE: self._handle_text_message,
            MessageType.FILE_TRANSFER_INIT: self._handle_file_transfer_init,
            MessageType.FILE_CHUNK: self._handle_file_chunk,
            MessageType.HEARTBEAT: self._handle_heartbeat,
            MessageType.ERROR: self._handle_error
        }
    
    async def start(self):
        """启动客户端"""
        self.running = True

        # 连接到中继服务器
        await self._connect_to_relays()

        # 注册用户到中继服务器
        await self._register_with_relays()

        # 启动后台任务
        tasks = [
            asyncio.create_task(self._heartbeat_loop()),
            asyncio.create_task(self._identity_rotation_loop()),
            asyncio.create_task(self._circuit_maintenance_loop()),
            asyncio.create_task(self._connection_monitor_loop())  # 新增连接监控
        ]

        try:
            # 让后台任务持续运行，不要等待它们完成
            print("✓ 客户端后台任务已启动")

            # 保持运行状态，让后台任务继续工作
            # 这样连接会保持活跃
            print("客户端进入保持运行状态...")
            while self.running:
                try:
                    await asyncio.sleep(5)  # 每5秒检查一次运行状态

                    # 发送心跳保持连接
                    if self.connections:
                        for relay_url, connection in list(self.connections.items()):
                            try:
                                if not (hasattr(connection, 'closed') and connection.closed):
                                    ping_msg = {
                                        "type": "ping",
                                        "timestamp": int(time.time())
                                    }
                                    await connection.send(json.dumps(ping_msg))
                                    print(f"发送心跳到 {relay_url}")
                            except Exception as e:
                                print(f"心跳发送失败 {relay_url}: {e}")
                                # 移除失效连接
                                if relay_url in self.connections:
                                    del self.connections[relay_url]

                except Exception as e:
                    print(f"保持运行循环出错: {e}")

        except asyncio.CancelledError:
            print("客户端任务被取消")
        except Exception as e:
            print(f"客户端启动任务出错: {e}")
        finally:
            print("客户端start方法结束，但连接保持活跃")
    
    async def stop(self):
        """停止客户端"""
        self.running = False
        
        # 关闭所有连接
        for connection in self.connections.values():
            await connection.close()
        self.connections.clear()
        
        # 清理电路
        for circuit_id in list(self.active_circuits.values()):
            self.onion_router.destroy_circuit(circuit_id)
        self.active_circuits.clear()
    
    async def _connect_to_relays(self):
        """连接到中继服务器"""
        for relay_url in self.relay_servers:
            try:
                connection = await websockets.connect(relay_url)
                self.connections[relay_url] = connection

                # 启动消息接收任务
                asyncio.create_task(self._handle_relay_messages(relay_url, connection))

                # 将中继服务器添加为洋葱路由节点
                self._add_relay_as_onion_node(relay_url)

                print(f"✓ 已连接到中继服务器: {relay_url}")
            except Exception as e:
                print(f"✗ 连接中继服务器失败 {relay_url}: {e}")

        # 显示可用的洋葱节点数量
        active_nodes = self.onion_router.get_active_nodes()
        print(f"✓ 可用洋葱节点数量: {len(active_nodes)}")

    def _add_relay_as_onion_node(self, relay_url: str):
        """将中继服务器添加为洋葱路由节点"""
        try:
            from .anonymizer import RelayNode
            import urllib.parse

            # 解析URL获取主机和端口
            parsed = urllib.parse.urlparse(relay_url)
            address = parsed.hostname or 'localhost'
            port = parsed.port or 8011

            # 生成节点ID
            node_id = f"relay_{address}_{port}"

            # 创建中继节点
            relay_node = RelayNode(
                node_id=node_id,
                address=address,
                port=port,
                public_key=b"dummy_public_key",  # 简化处理
                is_active=True
            )

            # 添加到洋葱路由器
            self.onion_router.add_relay_node(relay_node)
            print(f"✓ 已添加洋葱节点: {node_id}")

        except Exception as e:
            print(f"添加洋葱节点失败: {e}")

    async def _register_with_relays(self):
        """向中继服务器注册用户"""
        if not self.connections:
            raise Exception("没有可用的中继服务器连接")

        # 向所有服务器发送注册消息（不等待响应，避免并发recv问题）
        registration_sent = False
        for relay_url, connection in self.connections.items():
            try:
                register_message = {
                    "type": "register",
                    "user_id": self.user_id,
                    "display_name": self.user_id,
                    "timestamp": int(time.time())
                }

                await connection.send(json.dumps(register_message))
                print(f"✓ 已向 {relay_url} 发送注册消息")
                registration_sent = True

            except Exception as e:
                print(f"发送注册消息失败 {relay_url}: {e}")

        if not registration_sent:
            raise Exception("无法发送注册消息")

        # 等待一段时间让注册响应通过消息处理器处理
        await asyncio.sleep(2.0)

        # 检查是否收到注册成功响应（通过消息处理器设置）
        if not self.is_registered:
            # 给更多时间等待响应
            for i in range(5):
                await asyncio.sleep(1.0)
                if self.is_registered:
                    break

        if not self.is_registered:
            print("⚠ 注册状态未确认，但继续运行（可能已成功注册）")
            # 不抛出异常，因为我们看到了成功的注册确认消息
    
    async def _handle_relay_messages(self, relay_url: str, connection):
        """处理中继服务器消息"""
        try:
            async for message in connection:
                if not self.running:
                    break

                if isinstance(message, str):
                    # 尝试解析为简单JSON消息（中继服务器通信）
                    try:
                        data = json.loads(message)
                        await self._process_relay_message(data)
                    except json.JSONDecodeError:
                        # 如果不是JSON，尝试作为协议消息处理
                        await self._process_received_message(message)
                elif isinstance(message, bytes):
                    # 处理二进制消息（洋葱数据包）
                    await self._process_onion_packet(message)
        except websockets.exceptions.ConnectionClosed:
            print(f"与中继服务器 {relay_url} 的连接已断开")
            if relay_url in self.connections:
                del self.connections[relay_url]
        except Exception as e:
            print(f"处理中继消息时出错: {e}")

    async def _process_relay_message(self, data: Dict):
        """处理中继服务器的简单消息"""
        message_type = data.get('type')

        print(f"处理中继消息: {message_type}")
        print(f"消息详情: {data}")

        if message_type == 'register_success':
            self.is_registered = True
            print(f"✓ 注册成功确认: {data}")
            print(f"✓ 注册状态已设置: is_registered = {self.is_registered}")
        elif message_type == 'error':
            print(f"中继服务器错误: {data.get('error_message', 'Unknown error')}")
        elif message_type == 'user_lookup_response' or message_type == 'user_lookup_result':
            await self._handle_user_lookup_response(data)
        elif message_type == 'encrypted_message_received':
            print(f"收到加密消息，开始处理...")
            await self._handle_received_message(data)
        elif message_type == 'encrypted_file_received':
            print(f"收到加密文件，开始处理...")
            await self._handle_received_file(data)
        elif message_type == 'message_delivered':
            print(f"✓ 消息已送达: {data.get('target_user_id')}")
        elif message_type == 'file_delivered':
            print(f"✓ 文件已送达: {data.get('filename')} -> {data.get('target_user_id')}")
        elif message_type == 'message_sent':
            print(f"✓ 消息发送确认: {data.get('target_user_id')}")
        elif message_type in self.relay_message_handlers:
            try:
                await self.relay_message_handlers[message_type](data)
            except Exception as e:
                print(f"中继消息处理器出错 {message_type}: {e}")
        else:
            print(f"未处理的中继消息类型: {message_type}")

    async def _handle_received_message(self, data: Dict):
        """处理接收到的消息"""
        try:
            sender_user_id = data.get('sender_user_id')
            encrypted_content = data.get('encrypted_content')
            timestamp = data.get('timestamp')

            print(f"✓ 收到消息来自 {sender_user_id}: {encrypted_content}")

            # 触发消息接收事件（如果有GUI处理器）
            if 'message_received' in self.relay_message_handlers:
                await self.relay_message_handlers['message_received'](data)

        except Exception as e:
            print(f"处理接收消息失败: {e}")

    async def _handle_received_file(self, data: Dict):
        """处理接收到的文件"""
        try:
            sender_user_id = data.get('sender_user_id')
            filename = data.get('filename')
            file_size = data.get('file_size', 0)

            print(f"✓ 收到文件来自 {sender_user_id}: {filename} ({file_size} bytes)")

            # 触发文件接收事件（如果有GUI处理器）
            if 'file_received' in self.relay_message_handlers:
                await self.relay_message_handlers['file_received'](data)

        except Exception as e:
            print(f"处理接收文件失败: {e}")

    async def _handle_user_lookup_response(self, data: Dict):
        """处理用户查询响应"""
        try:
            target_user_id = data.get('target_user_id')
            is_online = data.get('is_online', False)
            query_id = data.get('query_id')

            print(f"收到用户查询响应: {target_user_id} -> {'在线' if is_online else '离线'} (查询ID: {query_id})")

            # 如果有等待的查询，设置结果
            if hasattr(self, '_pending_queries') and query_id and query_id in self._pending_queries:
                future = self._pending_queries[query_id]
                if not future.done():
                    future.set_result(is_online)
                del self._pending_queries[query_id]
                print(f"✓ 查询结果已设置: {query_id}")
            else:
                print(f"未找到对应的查询: {query_id}")

        except Exception as e:
            print(f"处理用户查询响应失败: {e}")

    def set_relay_message_handler(self, message_type: str, handler: Callable):
        """设置中继消息处理器"""
        self.relay_message_handlers[message_type] = handler
    
    async def _process_received_message(self, message_data: str):
        """处理接收到的消息"""
        try:
            protocol_message = self.protocol.deserialize_message(message_data)
            message_type = protocol_message.header.message_type
            
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](protocol_message)
            else:
                print(f"未知消息类型: {message_type}")
        except Exception as e:
            print(f"处理消息时出错: {e}")
    
    async def _process_onion_packet(self, packet: bytes):
        """处理洋葱数据包"""
        # 这里应该实现洋葱数据包的解包逻辑
        # 简化处理，直接解包最外层
        pass
    
    async def initiate_handshake(self, peer_id: str) -> bool:
        """发起握手"""
        try:
            # 生成临时密钥对
            private_key, public_key = self.key_exchange.generate_keypair()
            public_key_bytes = self.key_exchange.serialize_public_key(public_key)
            
            # 创建握手消息
            handshake_init = self.protocol.create_handshake_init(peer_id, public_key_bytes)
            
            # 通过匿名网络发送
            await self._send_anonymous_message(handshake_init, peer_id)
            
            # 存储临时密钥
            self.protocol.temp_keys = getattr(self.protocol, 'temp_keys', {})
            self.protocol.temp_keys[peer_id] = private_key
            
            return True
        except Exception as e:
            print(f"发起握手失败: {e}")
            return False
    
    async def send_text_message(self, peer_id: str, text: str) -> bool:
        """发送文本消息"""
        try:
            # 查找会话
            session_id = self._find_session_with_peer(peer_id)
            if not session_id:
                # 需要先建立会话
                if not await self.initiate_handshake(peer_id):
                    return False
                session_id = self._find_session_with_peer(peer_id)
            
            if not session_id:
                return False
            
            # 创建文本消息
            text_message = self.protocol.create_text_message(peer_id, text, session_id)
            
            # 通过匿名网络发送
            await self._send_anonymous_message(text_message, peer_id)
            
            return True
        except Exception as e:
            print(f"发送文本消息失败: {e}")
            return False
    
    async def send_file(self, peer_id: str, file_path: str) -> bool:
        """发送文件"""
        try:
            import os
            from ..crypto import SecureHash
            
            # 读取文件信息
            file_size = os.path.getsize(file_path)
            filename = os.path.basename(file_path)
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            file_hash = SecureHash.sha256_hex(file_data)
            
            # 查找会话
            session_id = self._find_session_with_peer(peer_id)
            if not session_id:
                return False
            
            # 发送文件传输初始化
            file_init = self.protocol.create_file_transfer_init(
                peer_id, filename, file_size, file_hash, session_id
            )
            await self._send_anonymous_message(file_init, peer_id)
            
            # 分块发送文件
            chunk_size = 64 * 1024  # 64KB
            total_chunks = (file_size + chunk_size - 1) // chunk_size
            transfer_id = file_init.payload['transfer_id']
            
            for i in range(total_chunks):
                start = i * chunk_size
                end = min(start + chunk_size, file_size)
                chunk_data = file_data[start:end]
                
                file_chunk = self.protocol.create_file_chunk(
                    peer_id, transfer_id, chunk_data, i, total_chunks, session_id
                )
                await self._send_anonymous_message(file_chunk, peer_id)
                
                # 添加延迟避免过快发送
                await asyncio.sleep(0.1)
            
            return True
        except Exception as e:
            print(f"发送文件失败: {e}")
            return False
    
    async def _send_anonymous_message(self, message: ProtocolMessage, peer_id: str):
        """通过匿名网络发送消息"""
        try:
            # 序列化消息
            message_data = self.protocol.serialize_message(message)
            
            # 获取或创建电路
            circuit_id = await self._get_or_create_circuit(peer_id)
            if not circuit_id:
                raise RuntimeError("无法建立匿名电路")
            
            # 创建洋葱数据包
            packet_data = message_data.encode('utf-8')
            
            # 添加流量混淆
            padded_packet = self.obfuscator.pad_packet(packet_data)
            
            # 通过电路发送
            await self._send_through_circuit(circuit_id, padded_packet, peer_id)
            
        except Exception as e:
            print(f"匿名发送消息失败: {e}")
            raise
    
    async def _get_or_create_circuit(self, peer_id: str) -> Optional[str]:
        """获取或创建到对等方的电路"""
        session_id = self._find_session_with_peer(peer_id)
        
        if session_id and session_id in self.active_circuits:
            return self.active_circuits[session_id]
        
        # 创建新电路
        circuit_id = f"circuit_{secrets.token_hex(8)}"
        if await self.onion_router.establish_circuit(circuit_id):
            if session_id:
                self.active_circuits[session_id] = circuit_id
            return circuit_id
        
        return None
    
    async def _send_through_circuit(self, circuit_id: str, data: bytes, destination: str):
        """通过电路发送数据"""
        try:
            # 创建洋葱数据包
            onion_packet = self.onion_router.create_onion_packet(circuit_id, data, destination)
            
            # 选择中继服务器发送
            if self.connections:
                relay_url = secrets.choice(list(self.connections.keys()))
                connection = self.connections[relay_url]
                await connection.send(onion_packet)
            else:
                raise RuntimeError("没有可用的中继连接")
                
        except Exception as e:
            print(f"通过电路发送数据失败: {e}")
            raise
    
    def _find_session_with_peer(self, peer_id: str) -> Optional[str]:
        """查找与对等方的会话"""
        for session_id, session in self.protocol.sessions.items():
            if session['peer_id'] == peer_id:
                return session_id
        return None
    
    # 消息处理器
    async def _handle_handshake_init(self, message: ProtocolMessage):
        """处理握手初始化"""
        try:
            # 生成响应密钥对
            private_key, public_key = self.key_exchange.generate_keypair()
            
            # 解析对方公钥
            peer_public_key_bytes = message.payload['public_key'].encode('ascii')
            peer_public_key = self.key_exchange.deserialize_public_key(
                base64.b64decode(peer_public_key_bytes)
            )
            
            # 执行密钥交换
            shared_key = self.key_exchange.perform_exchange(private_key, peer_public_key)
            
            # 创建响应
            public_key_bytes = self.key_exchange.serialize_public_key(public_key)
            handshake_response = self.protocol.create_handshake_response(
                message, public_key_bytes, shared_key
            )
            
            # 发送响应
            await self._send_anonymous_message(handshake_response, message.header.sender_id)
            
        except Exception as e:
            print(f"处理握手初始化失败: {e}")
    
    async def _handle_handshake_response(self, message: ProtocolMessage):
        """处理握手响应"""
        try:
            peer_id = message.header.sender_id
            
            # 获取临时私钥
            temp_keys = getattr(self.protocol, 'temp_keys', {})
            if peer_id not in temp_keys:
                return
            
            private_key = temp_keys[peer_id]
            
            # 解析对方公钥
            peer_public_key_bytes = base64.b64decode(message.payload['public_key'])
            peer_public_key = self.key_exchange.deserialize_public_key(peer_public_key_bytes)
            
            # 执行密钥交换
            shared_key = self.key_exchange.perform_exchange(private_key, peer_public_key)
            
            # 存储会话
            session_id = message.header.session_id
            self.protocol.sessions[session_id] = {
                'peer_id': peer_id,
                'session_key': shared_key,
                'created_at': int(time.time()),
                'last_activity': int(time.time())
            }
            
            # 发送完成消息
            handshake_complete = self.protocol.create_handshake_complete(message)
            await self._send_anonymous_message(handshake_complete, peer_id)
            
            # 清理临时密钥
            del temp_keys[peer_id]
            
            print(f"✓ 与 {peer_id} 的握手完成")
            
        except Exception as e:
            print(f"处理握手响应失败: {e}")
    
    async def _handle_handshake_complete(self, message: ProtocolMessage):
        """处理握手完成"""
        print(f"✓ 握手完成确认来自 {message.header.sender_id}")
    
    async def _handle_text_message(self, message: ProtocolMessage):
        """处理文本消息"""
        try:
            decrypted_text = self.protocol.decrypt_text_message(message)
            print(f"收到来自 {message.header.sender_id} 的消息: {decrypted_text}")
        except Exception as e:
            print(f"处理文本消息失败: {e}")
    
    async def _handle_file_transfer_init(self, message: ProtocolMessage):
        """处理文件传输初始化"""
        filename = message.payload['filename']
        file_size = message.payload['file_size']
        print(f"收到文件传输请求: {filename} ({file_size} 字节)")
    
    async def _handle_file_chunk(self, message: ProtocolMessage):
        """处理文件块"""
        try:
            chunk_data = self.protocol.decrypt_file_chunk(message)
            print(f"收到文件块 {message.header.chunk_index + 1}/{message.header.total_chunks}")
        except Exception as e:
            print(f"处理文件块失败: {e}")
    
    async def _handle_heartbeat(self, message: ProtocolMessage):
        """处理心跳"""
        # 更新会话活动时间
        session_id = message.header.session_id
        if session_id in self.protocol.sessions:
            self.protocol.sessions[session_id]['last_activity'] = int(time.time())
    
    async def _handle_error(self, message: ProtocolMessage):
        """处理错误消息"""
        error_code = message.payload['error_code']
        error_message = message.payload['error_message']
        print(f"收到错误: {error_code} - {error_message}")
    
    # 后台任务
    async def _heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            try:
                for session_id, session in list(self.protocol.sessions.items()):
                    peer_id = session['peer_id']
                    heartbeat = self.protocol.create_heartbeat(peer_id, session_id)
                    await self._send_anonymous_message(heartbeat, peer_id)
                
                await asyncio.sleep(30)  # 30秒心跳间隔
            except Exception as e:
                print(f"心跳循环出错: {e}")
                await asyncio.sleep(5)
    
    async def _identity_rotation_loop(self):
        """身份轮换循环"""
        while self.running:
            try:
                await asyncio.sleep(3600)  # 1小时轮换一次身份
                
                new_identity = self.identity_manager.rotate_identity()
                self.current_identity = new_identity
                print(f"✓ 身份已轮换: {new_identity}")
                
                # 清理旧身份
                self.identity_manager.cleanup_old_identities()
                
            except Exception as e:
                print(f"身份轮换出错: {e}")
    
    async def _circuit_maintenance_loop(self):
        """电路维护循环"""
        while self.running:
            try:
                await asyncio.sleep(600)  # 10分钟检查一次
                
                # 清理过期会话
                expired_count = self.protocol.cleanup_expired_sessions()
                if expired_count > 0:
                    print(f"✓ 清理了 {expired_count} 个过期会话")
                
                # 重建老化的电路
                # 这里可以添加电路重建逻辑
                
            except Exception as e:
                print(f"电路维护出错: {e}")

    async def _connection_monitor_loop(self):
        """连接监控循环"""
        while self.running:
            try:
                await asyncio.sleep(10)  # 每10秒检查一次，更频繁的心跳

                # 检查连接状态
                active_connections = []
                for relay_url, connection in list(self.connections.items()):
                    try:
                        # 检查WebSocket连接状态
                        if hasattr(connection, 'closed') and connection.closed:
                            print(f"检测到连接断开: {relay_url}")
                            del self.connections[relay_url]
                        else:
                            # 发送ping测试连接
                            ping_message = {
                                "type": "ping",
                                "timestamp": int(time.time())
                            }
                            await connection.send(json.dumps(ping_message))
                            active_connections.append(relay_url)
                    except Exception as e:
                        print(f"连接检查失败 {relay_url}: {e}")
                        if relay_url in self.connections:
                            del self.connections[relay_url]

                print(f"活跃连接数: {len(active_connections)}")

                # 如果连接数过少，尝试重连
                if len(active_connections) < 2:
                    print("连接数不足，尝试重连...")
                    await self._reconnect_to_relays()

            except Exception as e:
                print(f"连接监控出错: {e}")

    async def _reconnect_to_relays(self):
        """重新连接到中继服务器"""
        try:
            for relay_url in self.relay_servers:
                if relay_url not in self.connections:
                    try:
                        print(f"重新连接到: {relay_url}")
                        connection = await websockets.connect(relay_url)
                        self.connections[relay_url] = connection

                        # 启动消息接收任务
                        asyncio.create_task(self._handle_relay_messages(relay_url, connection))

                        # 重新注册用户
                        register_message = {
                            "type": "register",
                            "user_id": self.user_id,
                            "display_name": self.user_id,
                            "timestamp": int(time.time())
                        }
                        await connection.send(json.dumps(register_message))

                        print(f"✓ 重新连接成功: {relay_url}")

                    except Exception as e:
                        print(f"重新连接失败 {relay_url}: {e}")
        except Exception as e:
            print(f"重连过程出错: {e}")

    # GUI集成方法
    async def send_simple_message(self, target_user_id: str, message: str) -> bool:
        """发送简单消息（用于GUI）"""
        try:
            if not self.is_registered:
                print("用户未注册，无法发送消息")
                return False

            if not self.connections:
                print("没有可用的连接发送消息")
                return False

            # 向中继服务器发送加密消息
            for relay_url, connection in self.connections.items():
                try:
                    if hasattr(connection, 'closed') and connection.closed:
                        print(f"连接已关闭: {relay_url}")
                        continue

                    message_data = {
                        "type": "encrypted_message",
                        "target_user_id": target_user_id,
                        "encrypted_content": message,  # 简化版本，实际应该加密
                        "timestamp": int(time.time())
                    }

                    print(f"通过 {relay_url} 发送消息到 {target_user_id}: {message}")
                    await connection.send(json.dumps(message_data))
                    print(f"✓ 消息已发送到 {target_user_id}: {message}")
                    return True

                except Exception as e:
                    print(f"发送消息失败 {relay_url}: {e}")

            print("所有连接都无法发送消息")
            return False
        except Exception as e:
            print(f"发送简单消息失败: {e}")
            return False

    async def send_simple_file(self, target_user_id: str, file_path: str) -> bool:
        """发送简单文件（用于GUI）"""
        try:
            if not self.is_registered:
                print("用户未注册，无法发送文件")
                return False

            # 使用安全的文件发送
            return await self.send_file(target_user_id, file_path)
        except Exception as e:
            print(f"发送简单文件失败: {e}")
            return False

    async def lookup_user_status(self, target_user_id: str) -> bool:
        """查找用户状态（用于GUI）"""
        try:
            if not self.connections:
                print("没有可用的连接进行用户查询")
                return False

            # 创建查询结果等待器
            query_id = f"lookup_{target_user_id}_{int(time.time())}"
            result_future = asyncio.Future()

            # 临时存储查询结果
            if not hasattr(self, '_pending_queries'):
                self._pending_queries = {}
            self._pending_queries[query_id] = result_future

            # 向中继服务器查询用户状态
            print(f"开始查询，可用连接数: {len(self.connections)}")
            for relay_url, connection in self.connections.items():
                print(f"尝试通过连接查询: {relay_url}")
                print(f"连接对象类型: {type(connection)}")
                print(f"连接对象属性: {dir(connection)}")

                try:
                    # 检查连接状态
                    if hasattr(connection, 'closed'):
                        print(f"连接 {relay_url} closed 属性: {connection.closed}")
                        if connection.closed:
                            print(f"连接已关闭: {relay_url}")
                            continue
                    else:
                        print(f"连接 {relay_url} 没有 closed 属性")

                    lookup_message = {
                        "type": "user_lookup",
                        "target_user_id": target_user_id,
                        "query_id": query_id,
                        "timestamp": int(time.time())
                    }

                    print(f"向 {relay_url} 查询用户状态: {target_user_id} (查询ID: {query_id})")
                    print(f"发送查询消息: {json.dumps(lookup_message)}")

                    # 尝试发送消息
                    await connection.send(json.dumps(lookup_message))
                    print(f"✓ 查询消息已发送到 {relay_url}")

                    # 等待结果（通过消息处理器设置）
                    try:
                        is_online = await asyncio.wait_for(result_future, timeout=10.0)
                        print(f"✓ 用户状态查询结果: {target_user_id} -> {'在线' if is_online else '离线'}")
                        return is_online
                    except asyncio.TimeoutError:
                        print(f"用户状态查询超时: {target_user_id} from {relay_url}")

                    break  # 只查询第一个可用服务器

                except Exception as e:
                    print(f"❌ 用户查找失败 {relay_url}: {e}")
                    import traceback
                    print(f"详细错误信息: {traceback.format_exc()}")

            # 清理查询
            if query_id in self._pending_queries:
                del self._pending_queries[query_id]

            print(f"用户 {target_user_id} 查询完成，结果: 离线")
            return False
        except Exception as e:
            print(f"查找用户状态失败: {e}")
            return False

    def is_connected(self) -> bool:
        """检查是否已连接并注册"""
        return self.running and self.is_registered and len(self.connections) > 0


# 示例使用
if __name__ == "__main__":
    async def test_client():
        print("=== 匿名客户端测试 ===")
        
        relay_servers = [
            "ws://localhost:8001",
            "ws://localhost:8002",
            "ws://localhost:8003"
        ]
        
        client = AnonymousClient("TestUser", relay_servers)
        
        # 这里只是演示，实际需要运行的中继服务器
        print("客户端已创建，需要运行中继服务器才能完整测试")
        print(f"用户ID: {client.user_id}")
        print(f"当前身份: {client.current_identity}")
    
    asyncio.run(test_client())
